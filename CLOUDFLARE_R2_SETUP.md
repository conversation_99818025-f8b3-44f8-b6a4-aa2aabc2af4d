# Cloudflare R2 Setup Guide

This admin panel now uses Cloudflare R2 for video file storage instead of Cloudflare Stream. Follow this guide to set up R2 for your project.

## Prerequisites

1. A Cloudflare account
2. Access to Cloudflare R2 (may require a paid plan)

## Step 1: Create an R2 Bucket

1. Log in to your Cloudflare dashboard
2. Navigate to **R2 Object Storage** in the sidebar
3. Click **Create bucket**
4. Choose a unique bucket name (e.g., `your-app-videos`)
5. Select a location close to your users
6. Click **Create bucket**

## Step 2: Generate R2 API Tokens

1. In the Cloudflare dashboard, go to **My Profile** > **API Tokens**
2. Click **Create Token**
3. Use the **Custom token** template
4. Configure the token:
   - **Token name**: `R2 Admin Panel Access`
   - **Permissions**: 
     - Account - Cloudflare R2:Edit
   - **Account Resources**: Include - Your Account
   - **Zone Resources**: Include - All zones (or specific zones if needed)
5. Click **Continue to summary** and then **Create Token**
6. Copy the token and save it securely

## Step 3: Get R2 Access Keys

1. In the Cloudflare dashboard, go to **R2 Object Storage**
2. Click **Manage R2 API tokens**
3. Click **Create API token**
4. Configure the token:
   - **Token name**: `Admin Panel R2 Access`
   - **Permissions**: Admin Read & Write
   - **Specify bucket**: Select your bucket or leave for all buckets
5. Click **Create API token**
6. Copy both the **Access Key ID** and **Secret Access Key**

## Step 4: Configure Custom Domain (Optional but Recommended)

1. In your R2 bucket settings, go to **Settings** > **Custom Domains**
2. Click **Connect Domain**
3. Enter your domain (e.g., `cdn.yourdomain.com`)
4. Follow the DNS setup instructions
5. Wait for the domain to be verified

## Step 5: Update Environment Variables

Update your `.env.local` file with the following variables:

```env
# Cloudflare R2 Configuration
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_API_TOKEN=your-api-token
CLOUDFLARE_R2_ACCESS_KEY_ID=your-r2-access-key-id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
CLOUDFLARE_R2_BUCKET_NAME=your-bucket-name
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_PUBLIC_URL=https://your-custom-domain.com
```

### How to find your Account ID:
1. In the Cloudflare dashboard, go to the right sidebar
2. Your Account ID is displayed under **Account ID**

### R2 Endpoint Format:
- Replace `your-account-id` with your actual Cloudflare Account ID
- The endpoint format is: `https://[account-id].r2.cloudflarestorage.com`

### Public URL Options:
- **With Custom Domain**: `https://your-custom-domain.com`
- **Without Custom Domain**: `https://pub-[hash].r2.dev` (you can find this in your bucket settings)

## Step 6: Test the Setup

1. Start your development server: `npm run dev`
2. Navigate to `/videos/new`
3. Try uploading a video file
4. Watch the automatic thumbnail generation
5. Check that both video and thumbnail files appear in your R2 bucket

## Automatic Thumbnail Generation

The system now automatically generates thumbnails from uploaded videos:

### How it works:
1. **Frame Extraction**: Captures a frame at 10% of video duration
2. **Canvas Processing**: Uses HTML5 Canvas to process the video frame
3. **Image Optimization**: Converts to JPEG format with 80% quality
4. **Automatic Upload**: Uploads thumbnail to R2 alongside the video
5. **Preview Display**: Shows thumbnail preview during upload process

### Features:
- **Smart Timing**: Captures thumbnail at 10% of video duration (avoids black frames)
- **Size Optimization**: Limits thumbnail dimensions to 800x600 for performance
- **Format Standardization**: All thumbnails saved as JPEG for consistency
- **Fallback Support**: Option to provide custom thumbnail URL if needed
- **Real-time Preview**: Shows generated thumbnail before upload

### File Organization:
- Videos: `videos/timestamp-random-filename.ext`
- Thumbnails: `images/timestamp-random-filename_thumbnail.jpg`

## Features

### Video Upload
- Direct upload to R2 using presigned URLs
- Support for multiple video formats (MP4, WebM, OGG, AVI, MOV)
- File size validation (500MB limit)
- Upload progress tracking
- Automatic duration detection
- **Automatic thumbnail generation** from video frames
- Thumbnail preview during upload
- Custom thumbnail URL override option

### File Management
- Secure file uploads with presigned URLs
- Automatic file key generation with timestamps
- File type validation
- File size formatting utilities

### Security
- All uploads require authentication
- Role-based access control (admin/editor)
- Presigned URLs with expiration (1 hour)
- File type validation on both client and server

## API Endpoints

### POST /api/upload
Generate presigned URL for file upload
- Requires authentication
- Validates file type and size
- Returns upload URL and public URL

### DELETE /api/upload
Delete a file from R2
- Requires authentication
- Requires file key parameter

## Troubleshooting

### Common Issues

1. **"Access Denied" errors**
   - Check your R2 API tokens have the correct permissions
   - Verify your Account ID is correct

2. **"Bucket not found" errors**
   - Ensure the bucket name in your environment variables matches exactly
   - Check that the bucket exists in your account

3. **CORS errors**
   - R2 automatically handles CORS for presigned URLs
   - If you encounter issues, check your bucket CORS settings

4. **Upload failures**
   - Verify the presigned URL hasn't expired (1 hour limit)
   - Check file size limits (500MB default)
   - Ensure file type is supported

### Environment Variable Checklist

- [ ] `CLOUDFLARE_ACCOUNT_ID` - Your Cloudflare Account ID
- [ ] `CLOUDFLARE_R2_ACCESS_KEY_ID` - R2 Access Key ID
- [ ] `CLOUDFLARE_R2_SECRET_ACCESS_KEY` - R2 Secret Access Key
- [ ] `CLOUDFLARE_R2_BUCKET_NAME` - Your R2 bucket name
- [ ] `CLOUDFLARE_R2_ENDPOINT` - R2 endpoint URL
- [ ] `CLOUDFLARE_R2_PUBLIC_URL` - Public URL for accessing files

## Migration from Cloudflare Stream

If you're migrating from Cloudflare Stream:

1. Update your environment variables as shown above
2. The video upload form now uses file upload instead of Stream video IDs
3. Video URLs are now direct R2 URLs instead of Stream URLs
4. Thumbnails need to be generated separately (not automatic like Stream)

## Cost Considerations

- R2 storage: $0.015 per GB per month
- Class A operations (writes): $4.50 per million requests
- Class B operations (reads): $0.36 per million requests
- Data transfer: Free for the first 10GB per month

For detailed pricing, visit: https://developers.cloudflare.com/r2/pricing/
