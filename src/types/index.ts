import { ObjectId } from 'mongodb';

// Base interfaces
export interface BaseDocument {
  _id?: ObjectId | string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Video related types
export interface VideoUrls {
  original: string;  // Original video file URL from R2
  hls?: string;      // HLS stream URL (if processed)
  mp4_1080?: string; // 1080p MP4 URL (if processed)
  mp4_720?: string;  // 720p MP4 URL (if processed)
  mp4_480?: string;  // 480p MP4 URL (if processed)
}

export interface Video extends BaseDocument {
  title: string;
  titleAr?: string;
  description?: string;
  descriptionAr?: string;
  videoUrls: VideoUrls;
  thumbnailUrl?: string;
  duration: number;
  fileSize?: number;
  language: string;
  category?: string;
  tags?: string[];
  isActive: boolean;
  status: 'draft' | 'published' | 'archived';
  r2FileKey?: string; // R2 file key for the original video
  processingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  metadata?: {
    resolution?: string;
    bitrate?: number;
    codec?: string;
    frameRate?: number;
    originalFileName?: string;
  };
}

// Node related types
export interface Node extends BaseDocument {
  videoId: ObjectId | string;
  title: string;
  titleAr?: string;
  description?: string;
  descriptionAr?: string;
  startTime: number;
  endTime: number;
  autoAdvance: boolean;
  autoAdvanceDelay?: number;
  position?: {
    x: number;
    y: number;
  };
  isActive: boolean;
}

// Choice related types
export interface Choice extends BaseDocument {
  nodeId: ObjectId | string;
  label: string;
  labelAr?: string;
  description?: string;
  descriptionAr?: string;
  nextNodeId?: ObjectId | string;
  nextVideoId?: ObjectId | string;
  isCorrect?: boolean;
  points?: number;
  order: number;
  isActive: boolean;
  conditions?: {
    requiredScore?: number;
    requiredChoices?: string[];
  };
}

// Flow related types
export interface Flow extends BaseDocument {
  name: string;
  nameAr?: string;
  description?: string;
  descriptionAr?: string;
  startVideoId: ObjectId | string;
  startNodeId: ObjectId | string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number;
  language: string;
  isActive: boolean;
  status: 'draft' | 'published' | 'archived';
  tags?: string[];
  prerequisites?: string[];
  learningObjectives?: string[];
  version: number;
}

// Session related types
export interface Session extends BaseDocument {
  userId?: string;
  deviceId: string;
  flowId: ObjectId | string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  currentVideoId?: ObjectId | string;
  currentNodeId?: ObjectId | string;
  isCompleted: boolean;
  score?: number;
  progress: number;
  language: string;
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
  };
}

// Event related types
export type EventType = 
  | 'video_start'
  | 'video_end'
  | 'video_pause'
  | 'video_resume'
  | 'video_seek'
  | 'choice_selected'
  | 'choice_timeout'
  | 'node_enter'
  | 'node_exit'
  | 'flow_start'
  | 'flow_complete'
  | 'error'
  | 'user_action';

export interface Event extends BaseDocument {
  sessionId: ObjectId | string;
  type: EventType;
  timestamp: Date;
  videoId?: ObjectId | string;
  nodeId?: ObjectId | string;
  choiceId?: ObjectId | string;
  data?: Record<string, unknown>;
  duration?: number;
  position?: {
    x: number;
    y: number;
    z: number;
  };
}

// User related types
export interface User extends BaseDocument {
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer';
  avatar?: string;
  lastLogin?: Date;
  isActive: boolean;
  permissions: string[];
}

// Extended user type for NextAuth
export interface ExtendedUser {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer';
  avatar?: string;
  image?: string;
}

// Analytics types
export interface AnalyticsData {
  totalSessions: number;
  totalUsers: number;
  totalVideos: number;
  totalFlows: number;
  averageSessionDuration: number;
  avgSessionDuration: number;
  completionRate: number;
  sessionGrowth: number;
  userGrowth: number;
  peakUsageHour: number;
  avgScore: number;
  mostActiveDay: string;
  popularVideos: Array<{
    videoId: string;
    title: string;
    views: number;
    completionRate: number;
  }>;
  userEngagement: Array<{
    date: string;
    sessions: number;
    users: number;
  }>;
  deviceStats: Array<{
    platform: string;
    count: number;
    percentage: number;
  }>;
  languageStats: Array<{
    language: string;
    count: number;
    percentage: number;
  }>;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface VideoFormData {
  title: string;
  titleAr?: string;
  description?: string;
  descriptionAr?: string;
  videoUrls: VideoUrls;
  thumbnailUrl?: string;
  duration: number;
  language: string;
  category?: string;
  tags?: string[];
  status: 'draft' | 'published' | 'archived';
  isActive: boolean;
}

export type CreateVideoFormData = VideoFormData;
export type UpdateVideoFormData = Partial<VideoFormData>;

export interface NodeFormData {
  videoId: string;
  title: string;
  titleAr?: string;
  description?: string;
  descriptionAr?: string;
  startTime: number;
  endTime: number;
  autoAdvance: boolean;
  autoAdvanceDelay?: number;
  isActive: boolean;
}

export interface ChoiceFormData {
  nodeId: string;
  label: string;
  labelAr?: string;
  description?: string;
  descriptionAr?: string;
  nextNodeId?: string;
  nextVideoId?: string;
  isCorrect?: boolean;
  points?: number;
  order: number;
  isActive: boolean;
}

export interface FlowFormData {
  name: string;
  nameAr?: string;
  description?: string;
  descriptionAr?: string;
  startVideoId: string;
  startNodeId: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number;
  language: string;
  isActive: boolean;
  tags?: string[];
  prerequisites?: string[];
  learningObjectives?: string[];
}

// Filter and search types
export interface FilterOptions {
  search?: string;
  category?: string;
  language?: string;
  isActive?: boolean;
  status?: 'draft' | 'published' | 'archived';
  dateFrom?: Date;
  dateTo?: Date;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Dashboard types
export interface DashboardStats {
  totalVideos: number;
  totalFlows: number;
  totalSessions: number;
  totalUsers: number;
  recentSessions: Session[];
  popularVideos: Array<{
    video: Video;
    viewCount: number;
  }>;
  systemHealth: {
    status: 'healthy' | 'warning' | 'error';
    uptime: number;
    lastCheck: Date;
  };
}

// Cloudflare R2 types
export interface CloudflareR2File {
  key: string;
  url: string;
  publicUrl: string;
  size?: number;
  contentType?: string;
  lastModified?: Date;
  metadata?: Record<string, string>;
}

export interface R2UploadResult {
  uploadUrl: string;
  fileKey: string;
  publicUrl: string;
  expiresIn: number;
}

export interface R2FileMetadata {
  size?: number;
  contentType?: string;
  lastModified?: Date;
  metadata?: Record<string, string>;
}

// Export utility types
export type CreateVideoData = Omit<Video, '_id' | 'createdAt' | 'updatedAt'>;
export type UpdateVideoData = Partial<CreateVideoData>;
export type CreateNodeData = Omit<Node, '_id' | 'createdAt' | 'updatedAt'>;
export type UpdateNodeData = Partial<CreateNodeData>;
export type CreateChoiceData = Omit<Choice, '_id' | 'createdAt' | 'updatedAt'>;
export type UpdateChoiceData = Partial<CreateChoiceData>;
export type CreateFlowData = Omit<Flow, '_id' | 'createdAt' | 'updatedAt'>;
export type UpdateFlowData = Partial<CreateFlowData>;