import { Video } from '@/types';
import { formatDuration } from '@/lib/utils';
import Image from 'next/image';

interface PopularVideo extends Video {
  viewCount?: number;
  completionRate?: number;
}

interface PopularVideosProps {
  videos: PopularVideo[];
}

export function PopularVideos({ videos }: PopularVideosProps) {
  if (!videos || videos.length === 0) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Popular Videos</h3>
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
              <span className="text-2xl">🎬</span>
            </div>
            <p className="text-gray-500 font-medium">No videos available</p>
            <p className="text-sm text-gray-400 mt-1">Popular videos will appear here once content is uploaded</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Popular Videos</h3>
        <div className="space-y-3">
          {videos.slice(0, 5).map((video, index) => (
            <div 
              key={video._id?.toString()} 
              className="group relative p-4 bg-gradient-to-r from-white to-gray-50 rounded-xl border border-gray-100 hover:shadow-md transition-all duration-300 hover:scale-102 animate-fade-in"
              style={{animationDelay: `${index * 0.1}s`}}
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 relative">
                  <div className="w-20 h-14 bg-gradient-to-br from-gray-200 to-gray-300 rounded-xl overflow-hidden shadow-sm">
                    {video.thumbnailUrl ? (
                      <Image
                        src={video.thumbnailUrl}
                        alt={video.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-110"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100">
                        <span className="text-blue-500 text-lg">🎥</span>
                      </div>
                    )}
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white text-xs font-bold">#{index + 1}</span>
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-700 transition-colors duration-200">
                      {video.title}
                    </h4>
                    <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <button className="p-1 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors duration-200">
                        <span className="text-xs">👁️</span>
                      </button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3 mb-3">
                    <div className="text-center">
                      <div className="text-xs text-gray-500">Views</div>
                      <div className="text-sm font-bold text-gray-900">{(video.viewCount || 0).toLocaleString()}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-500">Duration</div>
                      <div className="text-sm font-bold text-gray-900">{formatDuration(video.duration || 0)}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-500">Completion</div>
                      <div className="text-sm font-bold text-gray-900">{video.completionRate || 0}%</div>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">Completion Rate</span>
                      <span className="font-medium text-gray-900">{video.completionRate || 0}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${video.completionRate || 0}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        {videos.length > 5 && (
          <div className="mt-6">
            <a
              href="/videos"
              className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 hover:scale-105"
            >
              <span className="mr-2">🎬</span>
              View all videos
              <span className="ml-2">→</span>
            </a>
          </div>
        )}
      </div>
    </div>
  );
}