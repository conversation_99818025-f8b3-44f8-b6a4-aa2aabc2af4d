import { Session } from '@/types';
import { formatDate } from '@/lib/utils';

interface RecentActivityProps {
  sessions: Session[];
}

export function RecentActivity({ sessions }: RecentActivityProps) {
  if (!sessions || sessions.length === 0) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
              <span className="text-2xl">📊</span>
            </div>
            <p className="text-gray-500 font-medium">No recent activity</p>
            <p className="text-sm text-gray-400 mt-1">Activity will appear here once users start sessions</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {sessions.slice(0, 5).map((session, index) => (
            <div 
              key={session._id?.toString()}
              className="group relative p-4 bg-gradient-to-r from-white to-gray-50 rounded-xl border border-gray-100 hover:shadow-md transition-all duration-300 hover:scale-102 animate-fade-in"
              style={{animationDelay: `${index * 0.1}s`}}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <span className="text-white text-xs font-bold">{session.deviceId ? session.deviceId.slice(-2) : 'XX'}</span>
                    </div>
                    <div>
                      <span className="text-sm font-semibold text-gray-900">
                        Device: {session.deviceId ? session.deviceId.slice(0, 8) + '...' : 'Unknown device'}
                      </span>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {session.language || 'Unknown'}
                        </span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          session.isCompleted 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {session.isCompleted ? '✓ Completed' : '⏳ In Progress'}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Progress</span>
                      <span className="font-medium text-gray-900">{Math.round(session.progress || 0)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${Math.round(session.progress || 0)}%` }}
                      ></div>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500 mt-3">
                      <div className="flex items-center space-x-4">
                        {session.duration && (
                          <span className="flex items-center">
                            <span className="mr-1">⏱️</span>
                            {Math.round(session.duration / 60)}m
                          </span>
                        )}
                        <span className="flex items-center">
                          <span className="mr-1">📅</span>
                          {formatDate(session.startTime || new Date())}
                        </span>
                      </div>
                      <span className="text-gray-400">
                        {new Date(session.startTime || new Date()).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="ml-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors duration-200">
                    <span className="text-sm">👁️</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        {sessions.length > 5 && (
          <div className="mt-6 text-center">
            <a
              href="/analytics"
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 hover:text-blue-700 transition-all duration-200 hover:scale-105"
            >
              <span className="mr-2">📈</span>
              View all activity
              <span className="ml-2">→</span>
            </a>
          </div>
        )}
      </div>
    </div>
  );
}