'use client';

import { useState, useEffect } from 'react';
import { DashboardStats } from '@/types';
import { LoadingSpinner } from './ui/LoadingSpinner';
import StatsCard from './ui/StatsCard';
import { RecentActivity } from './RecentActivity';
import { PopularVideos } from './PopularVideos';

export default function DashboardContent() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/dashboard');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-6 text-slate-600 text-base">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Page header */}
      <div className="app-header animate-slide-up">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-glow animate-float">
                <span className="text-3xl">🎯</span>
              </div>
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-4xl font-bold gradient-text mb-2">Dashboard</h1>
              <p className="text-gray-600 text-lg leading-relaxed">
                Welcome back! Here's what's happening with your VR training platform.
              </p>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Last updated</p>
              <p className="text-sm font-semibold text-gray-700">Just now</p>
            </div>
            <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Stats cards */}
      {stats && (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-10">
          <StatsCard
            title="Total Videos"
            value={stats.totalVideos}
            icon="📹"
            change={{ value: 12, type: 'increase' }}
            color="blue"
          />
          <StatsCard
            title="Total Users"
            value={stats.totalUsers}
            icon="👥"
            change={{ value: 8, type: 'increase' }}
            color="green"
          />
          <StatsCard
            title="Total Sessions"
            value={stats.totalSessions}
            icon="🎯"
            change={{ value: 5, type: 'increase' }}
            color="purple"
          />
          <StatsCard
            title="Total Flows"
            value={stats.totalFlows}
            icon="🔄"
            change={{ value: 3, type: 'increase' }}
            color="orange"
          />
        </div>
      )}

      {/* Content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Recent Activity</h2>
            <p className="text-base text-slate-600">Latest user sessions and interactions</p>
          </div>
          <div className="card-body">
            <RecentActivity sessions={stats?.recentSessions || []} />
          </div>
        </div>

        {/* Popular Videos */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Popular Videos</h2>
            <p className="text-base text-slate-600">Most viewed training content</p>
          </div>
          <div className="card-body">
            <PopularVideos videos={stats?.popularVideos?.map(item => ({
              ...item.video,
              viewCount: item.viewCount
            })) || []} />
          </div>
        </div>
      </div>

      {/* Enhanced Quick Actions */}
      <div className="mt-8">
        <div className="card card-elevated">
          <div className="card-header">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm">⚡</span>
              </div>
              <div>
                <h2 className="text-xl font-bold gradient-text-purple">Quick Actions</h2>
                <p className="text-sm text-gray-500">Common tasks and shortcuts</p>
              </div>
            </div>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <a
                href="/videos/new"
                className="group relative bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-500 hover:to-blue-600 rounded-2xl p-6 transition-all duration-300 hover:shadow-glow-blue hover:scale-105 border border-blue-200 hover:border-blue-400"
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all">
                    <span className="text-2xl">📹</span>
                  </div>
                  <div className="group-hover:text-white transition-colors">
                    <div className="font-bold text-lg">Add Video</div>
                    <div className="text-sm opacity-75">Upload new content</div>
                  </div>
                </div>
              </a>
              <a
                href="/flows/new"
                className="group relative bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-500 hover:to-purple-600 rounded-2xl p-6 transition-all duration-300 hover:shadow-glow-purple hover:scale-105 border border-purple-200 hover:border-purple-400"
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all">
                    <span className="text-2xl">🔄</span>
                  </div>
                  <div className="group-hover:text-white transition-colors">
                    <div className="font-bold text-lg">Create Flow</div>
                    <div className="text-sm opacity-75">Design training path</div>
                  </div>
                </div>
              </a>
              <a
                href="/analytics"
                className="group relative bg-gradient-to-br from-emerald-50 to-emerald-100 hover:from-emerald-500 hover:to-emerald-600 rounded-2xl p-6 transition-all duration-300 hover:shadow-glow-emerald hover:scale-105 border border-emerald-200 hover:border-emerald-400"
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all">
                    <span className="text-2xl">📊</span>
                  </div>
                  <div className="group-hover:text-white transition-colors">
                    <div className="font-bold text-lg">View Analytics</div>
                    <div className="text-sm opacity-75">Performance insights</div>
                  </div>
                </div>
              </a>
              <a
                href="/users"
                className="group relative bg-gradient-to-br from-gray-50 to-gray-100 hover:from-gray-500 hover:to-gray-600 rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:scale-105 border border-gray-200 hover:border-gray-400"
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all">
                    <span className="text-2xl">👥</span>
                  </div>
                  <div className="group-hover:text-white transition-colors">
                    <div className="font-bold text-lg">Manage Users</div>
                    <div className="text-sm opacity-75">User administration</div>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
