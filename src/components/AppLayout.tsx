'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Navigation } from './Navigation';
import { LoadingSpinner } from './ui/LoadingSpinner';

interface AppLayoutProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

export function AppLayout({ children, requireAuth = true }: AppLayoutProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading
    
    if (requireAuth && !session) {
      router.push('/auth/signin');
      return;
    }
  }, [session, status, router, requireAuth]);

  // Show loading spinner while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center animate-fade-in">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 mx-auto shadow-lg">
            <LoadingSpinner size="lg" variant="white" />
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
            Loading Dashboard
          </h2>
          <p className="text-gray-600">Preparing your VR training platform...</p>
        </div>
      </div>
    );
  }

  // If auth is required but user is not authenticated, don't render anything
  // (useEffect will handle the redirect)
  if (requireAuth && !session) {
    return null;
  }

  // For auth pages, render without sidebar
  if (!requireAuth) {
    return <>{children}</>;
  }

  // For authenticated pages, render with sidebar layout
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Main content area - accounts for fixed sidebar on desktop and mobile header */}
      <div className="lg:pl-64 pt-16 lg:pt-0">
        <main className="min-h-screen">
          <div className="p-4 lg:p-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
