import { forwardRef } from 'react';
import { cn } from '@/lib/utils';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'published' | 'draft' | 'archived' | 'active' | 'inactive' | 'pending' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  dot?: boolean;
}

const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = 'published', size = 'md', dot = false, children, ...props }, ref) => {
    const baseClasses = 'status-badge';
    
    const variantClasses = {
      published: 'status-published',
      draft: 'status-draft',
      archived: 'status-archived',
      active: 'status-active',
      inactive: 'status-inactive',
      pending: 'status-pending',
      warning: 'status-warning',
    };

    const sizeClasses = {
      sm: 'px-2 py-1 text-xs',
      md: 'px-3 py-1 text-xs',
      lg: 'px-4 py-2 text-sm',
    };

    return (
      <span
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        {...props}
      >
        {dot && (
          <span className="w-1.5 h-1.5 bg-current rounded-full mr-1.5 opacity-75" />
        )}
        {children}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

export { Badge };
export type { BadgeProps };
