import { cn } from '@/lib/utils';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon?: React.ComponentType<{ className?: string }> | string;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
}

export default function StatsCard({ title, value, icon: Icon, color = 'blue', change }: StatsCardProps) {
  const colorClasses = {
    blue: {
      bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
      text: 'text-blue-600',
      border: 'border-blue-200',
      icon: 'text-blue-500',
      accent: 'bg-blue-500',
      iconBg: 'bg-gradient-to-br from-blue-500 to-blue-600'
    },
    green: {
      bg: 'bg-gradient-to-br from-green-50 to-green-100',
      text: 'text-green-600',
      border: 'border-green-200',
      icon: 'text-green-500',
      accent: 'bg-green-500',
      iconBg: 'bg-gradient-to-br from-green-500 to-green-600'
    },
    purple: {
      bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
      text: 'text-purple-600',
      border: 'border-purple-200',
      icon: 'text-purple-500',
      accent: 'bg-purple-500',
      iconBg: 'bg-gradient-to-br from-purple-500 to-purple-600'
    },
    orange: {
      bg: 'bg-gradient-to-br from-orange-50 to-orange-100',
      text: 'text-orange-600',
      border: 'border-orange-200',
      icon: 'text-orange-500',
      accent: 'bg-orange-500',
      iconBg: 'bg-gradient-to-br from-orange-500 to-orange-600'
    },
    red: {
      bg: 'bg-gradient-to-br from-red-50 to-red-100',
      text: 'text-red-600',
      border: 'border-red-200',
      icon: 'text-red-500',
      accent: 'bg-red-500',
      iconBg: 'bg-gradient-to-br from-red-500 to-red-600'
    },
  };

  return (
    <div className={cn(
      "card group animate-fade-in",
      "hover:shadow-glow hover:-translate-y-1",
      colorClasses[color].border
    )}>
      <div className="card-body">
        <div className="flex items-center justify-between mb-4">
          <div className={cn(
            "w-12 h-12 rounded-xl flex items-center justify-center shadow-lg",
            "group-hover:scale-110 transition-transform duration-300",
            colorClasses[color].iconBg
          )}>
            {typeof Icon === 'string' ? (
              <span className="text-2xl">{Icon}</span>
            ) : Icon ? (
              <Icon className="h-6 w-6 text-white" />
            ) : null}
          </div>
          <div className={cn(
            "w-3 h-3 rounded-full animate-pulse",
            colorClasses[color].accent
          )}></div>
        </div>
        
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">
            {title}
          </h3>
          <p className={cn(
            "text-3xl font-bold transition-colors duration-300",
            "group-hover:" + colorClasses[color].text
          )}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
        </div>
        
        {change && (
          <div className="mt-4 flex items-center justify-between pt-4 border-t border-gray-100">
            <div className="flex items-center space-x-2">
              <div className={cn(
                "w-2 h-2 rounded-full",
                change.type === 'increase' ? 'bg-green-500' : 'bg-red-500'
              )}></div>
              <span className={cn(
                "text-sm font-medium",
                change.type === 'increase' ? 'text-green-600' : 'text-red-600'
              )}>
                {change.type === 'increase' ? '+' : ''}{change.value}%
              </span>
            </div>
            <span className="text-xs text-gray-400">vs last month</span>
          </div>
        )}
      </div>
    </div>
  );
}