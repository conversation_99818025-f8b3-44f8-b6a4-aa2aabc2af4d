import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'primary' | 'secondary' | 'white';
  className?: string;
  text?: string;
}

export function LoadingSpinner({
  size = 'md',
  variant = 'primary',
  className,
  text
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-6 w-6 border-2',
    lg: 'h-8 w-8 border-3',
    xl: 'h-12 w-12 border-4',
  };

  const variantClasses = {
    primary: 'border-gray-200 border-t-blue-600',
    secondary: 'border-gray-300 border-t-gray-600',
    white: 'border-white/30 border-t-white',
  };

  if (text) {
    return (
      <div className="flex flex-col items-center justify-center space-y-3">
        <div
          className={cn(
            'animate-spin rounded-full',
            sizeClasses[size],
            variantClasses[variant],
            className
          )}
        />
        <p className="text-sm text-gray-600 font-medium animate-pulse">
          {text}
        </p>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'animate-spin rounded-full',
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
    />
  );
}