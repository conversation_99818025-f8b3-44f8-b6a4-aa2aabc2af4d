'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { AnalyticsData } from '@/types';
import { formatDuration } from '@/lib/utils';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AppLayout } from '@/components/AppLayout';
import StatsCard from '@/components/ui/StatsCard';
import {
  ChartBarIcon,
  UsersIcon,
  VideoCameraIcon,
  ClockIcon,
  TrophyIcon,
  DevicePhoneMobileIcon,
  CalendarDaysIcon,
} from '@heroicons/react/24/outline';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import toast from 'react-hot-toast';

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

export default function AnalyticsPage() {
  const { status } = useSession();
  const router = useRouter();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('30'); // days
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  });

  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        timeframe,
        startDate: dateRange.start,
        endDate: dateRange.end,
      });

      const response = await fetch(`/api/analytics?${params}`);
      if (!response.ok) throw new Error('Failed to fetch analytics');

      const result = await response.json();
      setAnalytics(result.data || result); // Handle both {data: ...} and direct response
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  }, [timeframe, dateRange.start, dateRange.end]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }
    if (status === 'authenticated') {
      fetchAnalytics();
    }
  }, [status, timeframe, router, fetchAnalytics]);

  const handleTimeframeChange = (newTimeframe: string) => {
    setTimeframe(newTimeframe);
    const days = parseInt(newTimeframe);
    setDateRange({
      start: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0],
    });
  };

  if (status === 'loading' || loading) {
    return (
      <div className="app-container flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <AppLayout>
        <div className="app-content">
          <div className="flex items-center justify-center min-h-96">
            <div className="text-center">
              <ChartBarIcon className="mx-auto h-12 w-12 text-slate-400" />
              <h3 className="mt-4 text-lg font-medium text-slate-900">No analytics data</h3>
              <p className="mt-2 text-base text-slate-600">Analytics data will appear here once users start training.</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Ensure all required properties exist with default values
  const safeAnalytics = {
    totalSessions: analytics.totalSessions || 0,
    totalUsers: analytics.totalUsers || 0,
    totalVideos: analytics.totalVideos || 0,
    totalFlows: analytics.totalFlows || 0,
    avgSessionDuration: analytics.avgSessionDuration || analytics.averageSessionDuration || 0,
    completionRate: analytics.completionRate || 0,
    sessionGrowth: analytics.sessionGrowth || 0,
    userGrowth: analytics.userGrowth || 0,
    peakUsageHour: analytics.peakUsageHour || 0,
    avgScore: analytics.avgScore || 0,
    mostActiveDay: analytics.mostActiveDay || 'N/A',
    popularVideos: analytics.popularVideos || [],
    userEngagement: analytics.userEngagement || [],
    deviceStats: analytics.deviceStats || [],
    languageStats: analytics.languageStats || []
  };

  return (
    <AppLayout>
      <div className="app-content">
        {/* Header */}
        <div className="app-header animate-slide-up mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                <ChartBarIcon className="h-7 w-7 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 via-slate-800 to-slate-700 bg-clip-text text-transparent">
                  Analytics Dashboard
                </h1>
                <p className="text-lg text-slate-600 mt-2 leading-relaxed">
                  Track user engagement and content performance across your VR training platform
                </p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="text-right">
                <p className="text-base text-slate-500">Last Updated</p>
                <div className="flex items-center space-x-3 mt-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-base font-medium text-green-600">Real-time</span>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-8 flex flex-wrap items-center justify-between gap-6">
            <div className="flex items-center space-x-4">
              <select
                value={timeframe}
                onChange={(e) => handleTimeframeChange(e.target.value)}
                className="px-6 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-base bg-white"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
              </select>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          <StatsCard
            title="Total Sessions"
            value={safeAnalytics.totalSessions}
            icon={UsersIcon}
            color="blue"
            change={safeAnalytics.sessionGrowth ? {
              value: safeAnalytics.sessionGrowth,
              type: safeAnalytics.sessionGrowth > 0 ? 'increase' : 'decrease'
            } : undefined}
          />
          <StatsCard
            title="Active Users"
            value={safeAnalytics.totalUsers}
            icon={DevicePhoneMobileIcon}
            color="green"
            change={safeAnalytics.userGrowth ? {
              value: safeAnalytics.userGrowth,
              type: safeAnalytics.userGrowth > 0 ? 'increase' : 'decrease'
            } : undefined}
          />
          <StatsCard
            title="Avg. Session Duration"
            value={formatDuration(safeAnalytics.avgSessionDuration)}
            icon={ClockIcon}
            color="purple"
          />
          <StatsCard
            title="Completion Rate"
            value={`${Math.round(safeAnalytics.completionRate)}%`}
            icon={TrophyIcon}
            color="orange"
          />
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* User Engagement Over Time */}
          <div className="bg-white p-8 rounded-2xl shadow-lg border border-slate-200">
            <h3 className="text-xl font-semibold text-slate-900 mb-6">User Engagement Over Time</h3>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={safeAnalytics.userEngagement}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="sessions"
                  stackId="1"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.6}
                  name="Sessions"
                />
                <Area
                  type="monotone"
                  dataKey="completions"
                  stackId="1"
                  stroke="#10B981"
                  fill="#10B981"
                  fillOpacity={0.6}
                  name="Completions"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* Popular Videos */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Most Popular Videos</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={safeAnalytics.popularVideos} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="title" type="category" width={100} />
                <Tooltip />
                <Bar dataKey="views" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Device and Language Stats */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Device Distribution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Device Distribution</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={safeAnalytics.deviceStats}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {(safeAnalytics.deviceStats).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Language Distribution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Language Distribution</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={safeAnalytics.languageStats}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {(safeAnalytics.languageStats).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Detailed Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Performing Videos */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Top Performing Videos</h3>
            </div>
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Video
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Views
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completion
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {(safeAnalytics.popularVideos).slice(0, 5).map((video, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <VideoCameraIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div className="text-sm font-medium text-gray-900 truncate max-w-32">
                            {video.title || 'Untitled Video'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {video.views || 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm text-gray-900">
                            {Math.round(video.completionRate || 0)}%
                          </div>
                          <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${video.completionRate || 0}%` }}
                            />
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Recent Activity Summary */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Activity Summary</h3>
            </div>
            <div className="p-6">
              <dl className="space-y-4">
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Total Videos</dt>
                  <dd className="text-sm text-gray-900">{safeAnalytics.totalVideos}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Total Flows</dt>
                  <dd className="text-sm text-gray-900">{safeAnalytics.totalFlows}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Peak Usage Hour</dt>
                  <dd className="text-sm text-gray-900">
                    {safeAnalytics.peakUsageHour ? `${safeAnalytics.peakUsageHour}:00` : 'N/A'}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Avg. Session Score</dt>
                  <dd className="text-sm text-gray-900">
                    {safeAnalytics.avgScore ? `${Math.round(safeAnalytics.avgScore)}%` : 'N/A'}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Most Active Day</dt>
                  <dd className="text-sm text-gray-900">
                    {safeAnalytics.mostActiveDay || 'N/A'}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>

        {/* Export Options */}
        <div className="mt-8 bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Export Data</h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => {
                // Export functionality would be implemented here
                toast.success('Export feature coming soon!');
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <CalendarDaysIcon className="-ml-1 mr-2 h-5 w-5" />
              Export Sessions
            </button>
            <button
              onClick={() => {
                // Export functionality would be implemented here
                toast.success('Export feature coming soon!');
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ChartBarIcon className="-ml-1 mr-2 h-5 w-5" />
              Export Analytics
            </button>
            <button
              onClick={() => {
                // Export functionality would be implemented here
                toast.success('Export feature coming soon!');
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <UsersIcon className="-ml-1 mr-2 h-5 w-5" />
              Export Users
            </button>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}