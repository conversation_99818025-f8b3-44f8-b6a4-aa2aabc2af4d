'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { FlowFormData, Video } from '@/types';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AppLayout } from '@/components/AppLayout';
import { ArrowLeftIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function NewFlowPage() {
  const { status } = useSession();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [videos, setVideos] = useState<Video[]>([]);

  const { register, handleSubmit, formState: { errors } } = useForm<FlowFormData>({
    defaultValues: {
      language: 'en',
      difficulty: 'beginner',
      estimatedDuration: 10,
      isActive: true,
    },
  });

  useEffect(() => {
    if (status === 'authenticated') {
      void fetchVideos();
    }
  }, [status]);

  const fetchVideos = async () => {
    try {
      const res = await fetch('/api/videos?limit=100');
      const data = await res.json();
      setVideos(data.data || data.videos || []);
    } catch (e) {
      console.error('Failed to load videos', e);
    }
  };

  const onSubmit = async (form: FlowFormData) => {
    setIsSubmitting(true);
    try {
      const res = await fetch('/api/flows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(form),
      });

      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        throw new Error(err.error || 'Failed to create flow');
      }

      toast.success('Flow created successfully');
      router.push('/flows');
    } catch (err: unknown) {
      toast.error(err instanceof Error ? err.message : 'Failed to create flow');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  return (
    <AppLayout>
      <div className="app-content">
        <div className="app-header">
          <div className="flex items-center space-x-4">
            <Link href="/flows" className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700">
              <ArrowLeftIcon className="icon mr-1" />
              Back to Flows
            </Link>
          </div>
          <h1 className="mt-4 text-3xl font-bold text-gray-900">Create New Flow</h1>
          <p className="mt-2 text-sm text-gray-600">Define a starting video and node, and basic properties.</p>
        </div>

        <div className="card">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-6">
            <div className="grid grid-cols-1 gap-6">
              <div className="form-group">
                <label className="form-label">Name *</label>
                <input {...register('name', { required: 'Name is required' })} className="form-input" placeholder="Flow name" />
                {errors.name && <p className="form-error">{errors.name.message}</p>}
              </div>

              <div className="form-group">
                <label className="form-label">Description</label>
                <textarea {...register('description')} rows={3} className="form-textarea" placeholder="Optional description" />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="form-group">
                  <label className="form-label">Category *</label>
                  <input {...register('category', { required: 'Category is required' })} className="form-input" placeholder="e.g. safety" />
                  {errors.category && <p className="form-error">{errors.category.message}</p>}
                </div>
                <div className="form-group">
                  <label className="form-label">Difficulty *</label>
                  <select {...register('difficulty', { required: true })} className="form-select">
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="form-group">
                  <label className="form-label">Language *</label>
                  <select {...register('language', { required: true })} className="form-select">
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">Estimated Duration (min) *</label>
                  <input type="number" min={1} {...register('estimatedDuration', { valueAsNumber: true, required: true })} className="form-input" />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="form-group">
                  <label className="form-label">Start Video *</label>
                  <select {...register('startVideoId', { required: 'Start video is required' })} className="form-select">
                    <option value="">Select a video</option>
                    {videos.map(v => (
                      <option key={String(v._id)} value={String(v._id)}>{v.title}</option>
                    ))}
                  </select>
                  {errors.startVideoId && <p className="form-error">{errors.startVideoId.message}</p>}
                </div>
                <div className="form-group">
                  <label className="form-label">Start Node ID *</label>
                  <input {...register('startNodeId', { required: 'Start node ID is required' })} className="form-input" placeholder="Paste node _id" />
                  {errors.startNodeId && <p className="form-error">{errors.startNodeId.message}</p>}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <input id="isActive" type="checkbox" {...register('isActive')} className="h-4 w-4 text-blue-600 border-gray-300 rounded" />
                <label htmlFor="isActive" className="text-sm text-gray-700">Active</label>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-4 flex justify-end gap-3">
              <Link href="/flows" className="btn-secondary">Cancel</Link>
              <button type="submit" disabled={isSubmitting} className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed">
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <DocumentDuplicateIcon className="icon-sm mr-2" />
                    Create Flow
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  );
}

