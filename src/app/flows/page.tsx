'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Flow, FilterOptions } from '@/types';
import { formatDate } from '@/lib/utils';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AppLayout } from '@/components/AppLayout';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentDuplicateIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PlayIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function FlowsPage() {
  const { status } = useSession();
  const router = useRouter();
  const [flows, setFlows] = useState<Flow[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [totalPages, setTotalPages] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  const fetchFlows = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: filters.page?.toString() || '1',
        limit: filters.limit?.toString() || '10',
        sortBy: filters.sortBy || 'createdAt',
        sortOrder: filters.sortOrder || 'desc',
        ...(searchTerm && { search: searchTerm }),
        ...(filters.language && { language: filters.language }),
        ...(filters.status && { status: filters.status }),
      });

      const response = await fetch(`/api/flows?${params}`);
      if (!response.ok) throw new Error('Failed to fetch flows');

      const data = await response.json();
      setFlows(data.flows || []);
      setTotalPages(data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching flows:', error);
      toast.error('Failed to load flows');
    } finally {
      setLoading(false);
    }
  }, [filters, searchTerm]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }
    if (status === 'authenticated') {
      fetchFlows();
    }
  }, [status, fetchFlows, router]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({ ...prev, page: 1 }));
    fetchFlows();
  };

  const handleDelete = async (flowId: string) => {
    if (!confirm('Are you sure you want to delete this flow? This action cannot be undone.')) return;

    try {
      const response = await fetch(`/api/flows/${flowId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete flow');
      }

      toast.success('Flow deleted successfully');
      fetchFlows();
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete flow');
    }
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (status === 'loading' || loading) {
    return (
      <div className="page-container flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 animate-pulse">Loading training flows...</p>
        </div>
      </div>
    );
  }

  return (
    <AppLayout>
      <div className="app-content">
        {/* Header */}
        <div className="app-header animate-slide-up mb-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                  <DocumentDuplicateIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 via-slate-800 to-slate-700 bg-clip-text text-transparent">
                    Training Flows
                  </h1>
                  <p className="text-lg text-slate-600 mt-2 leading-relaxed">
                    Create and manage interactive training sequences
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-8 text-base text-slate-500 mt-6">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="font-medium">{flows.length} Flows Available</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                  <span className="font-medium">Interactive Content</span>
                </div>
              </div>
            </div>
            <div className="mt-8 md:mt-0">
              <Link
                href="/flows/new"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                <PlusIcon className="h-5 w-5 mr-3" />
                Create New Flow
              </Link>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="card animate-fade-in mb-8">
          <div className="card-header">
            <h3 className="text-xl font-semibold text-slate-900 mb-3">Search & Filter</h3>
            <p className="text-base text-slate-600">Find and organize your training flow sequences</p>
          </div>
          <div className="card-body">
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-6">
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <input
                    type="text"
                    placeholder="Search by title, description, or tags..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 text-base"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => setShowFilters(!showFilters)}
                  className={`px-6 py-3 border border-slate-300 rounded-xl font-medium transition-all duration-300 ${
                    showFilters 
                      ? 'ring-2 ring-purple-500 bg-purple-50 text-purple-700 border-purple-500' 
                      : 'text-slate-700 hover:bg-slate-50 hover:border-slate-400'
                  }`}
                >
                  <FunnelIcon className="h-5 w-5 mr-2 inline" />
                  Filters
                  {showFilters && <span className="ml-2 w-2 h-2 bg-purple-500 rounded-full"></span>}
                </button>
                <button
                  type="submit"
                  className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <MagnifyingGlassIcon className="h-5 w-5 mr-2 inline" />
                  Search
                </button>
              </div>
            </form>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Language
                    </label>
                    <select
                      value={filters.language || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, language: e.target.value || undefined }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">All Languages</option>
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={filters.status || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value === '' ? undefined : e.target.value as 'draft' | 'published' | 'archived' }))}
                      className="form-select"
                    >
                      <option value="">All Status</option>
                      <option value="draft">Draft</option>
                      <option value="published">Published</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sort By
                    </label>
                    <select
                      value={`${filters.sortBy}-${filters.sortOrder}`}
                      onChange={(e) => {
                        const [sortBy, sortOrder] = e.target.value.split('-');
                        setFilters(prev => ({ ...prev, sortBy, sortOrder: sortOrder as 'asc' | 'desc' }));
                      }}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="createdAt-desc">Newest First</option>
                      <option value="createdAt-asc">Oldest First</option>
                      <option value="title-asc">Title A-Z</option>
                      <option value="title-desc">Title Z-A</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Flows List */}
        {flows.length === 0 ? (
          <div className="text-center py-12">
            <DocumentDuplicateIcon className="mx-auto icon-lg text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No flows</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating a new training flow.
            </p>
            <div className="mt-6">
              <Link
                href="/flows/new"
                className="btn-primary"
              >
                <PlusIcon className="icon-sm mr-2" />
                New Flow
              </Link>
            </div>
          </div>
        ) : (
          <div className="card">
            <ul className="divide-y divide-gray-200">
              {flows.map((flow) => (
                <li key={flow._id?.toString()}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                            <DocumentDuplicateIcon className="icon text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="flex items-center">
                            <p className="text-sm font-medium text-blue-600 truncate">
                              {flow.name}
                            </p>
                            <span className={
                              flow.status === 'published'
                                ? 'status-published'
                                : flow.status === 'draft'
                                ? 'status-draft'
                                : 'status-inactive'
                            }>
                              {flow.status}
                            </span>
                          </div>
                          <div className="mt-2 flex items-center text-sm text-gray-500">
                            <p className="truncate">{flow.description}</p>
                          </div>
                          <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                            <span>Language: {flow.language?.toUpperCase()}</span>
                            <span>•</span>
                            <span>Duration: {flow.estimatedDuration} min</span>
                            <span>•</span>
                            <span>Created: {formatDate(flow.createdAt || new Date())}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Link
                          href={`/flows/${flow._id}`}
                          className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/flows/${flow._id}/edit`}
                          className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Link>
                        <button
                          onClick={() => handleDelete(flow._id?.toString() || '')}
                          className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    
                    {/* Flow Preview */}
                    <div className="mt-4">
                      <div className="text-sm text-gray-500">
                        Interactive training flow • {flow.estimatedDuration || 0} min duration
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 flex items-center justify-between">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(Math.max(1, (filters.page || 1) - 1))}
                disabled={(filters.page || 1) <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(Math.min(totalPages, (filters.page || 1) + 1))}
                disabled={(filters.page || 1) >= totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{filters.page || 1}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          page === (filters.page || 1)
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        } ${page === 1 ? 'rounded-l-md' : ''} ${page === Math.min(totalPages, 5) ? 'rounded-r-md' : ''}`}
                      >
                        {page}
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
}