import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { ObjectId, Filter, Document } from 'mongodb';
import { authOptions } from '@/lib/auth';
import { getCollection } from '@/lib/mongodb';
import { CreateFlowData } from '@/types';

// GET /api/flows - Get all flows with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const language = searchParams.get('language') || '';
    const difficulty = searchParams.get('difficulty') || '';
    const isActive = searchParams.get('isActive');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const flows = await getCollection('flows');
    
    // Build filter query
    const filter: Filter<Document> = {};
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { nameAr: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { descriptionAr: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (category) {
      filter.category = category;
    }
    
    if (language) {
      filter.language = language;
    }
    
    if (difficulty) {
      filter.difficulty = difficulty;
    }
    
    if (isActive !== null) {
      filter.isActive = isActive === 'true';
    }

    // Get total count
    const total = await flows.countDocuments(filter);
    
    // Get paginated results with populated data
    const skip = (page - 1) * limit;
    const sort: Record<string, 1 | -1> = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'videos',
          localField: 'startVideoId',
          foreignField: '_id',
          as: 'startVideo'
        }
      },
      {
        $lookup: {
          from: 'nodes',
          localField: 'startNodeId',
          foreignField: '_id',
          as: 'startNode'
        }
      },
      {
        $addFields: {
          startVideo: { $arrayElemAt: ['$startVideo', 0] },
          startNode: { $arrayElemAt: ['$startNode', 0] }
        }
      },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const results = await flows.aggregate(pipeline).toArray();

    return NextResponse.json({
      success: true,
      data: results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching flows:', error);
    return NextResponse.json(
      { error: 'Failed to fetch flows' },
      { status: 500 }
    );
  }
}

// POST /api/flows - Create a new flow
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session.user.role !== 'admin' && session.user.role !== 'editor') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();

    // Convert string IDs to ObjectId
    let startVideoId: ObjectId;
    let startNodeId: ObjectId;

    try {
      startVideoId = new ObjectId(body.startVideoId);
      startNodeId = new ObjectId(body.startNodeId);
    } catch {
      return NextResponse.json(
        { error: 'Invalid video ID or node ID format' },
        { status: 400 }
      );
    }

    const flowData: CreateFlowData = {
      name: body.name,
      nameAr: body.nameAr,
      description: body.description,
      descriptionAr: body.descriptionAr,
      startVideoId,
      startNodeId,
      category: body.category,
      difficulty: body.difficulty,
      estimatedDuration: body.estimatedDuration,
      language: body.language,
      isActive: body.isActive !== false,
      status: 'draft',
      tags: body.tags || [],
      prerequisites: body.prerequisites || [],
      learningObjectives: body.learningObjectives || [],
      version: 1
    };

    // Validate required fields
    if (!flowData.name || !flowData.startVideoId || !flowData.startNodeId || 
        !flowData.category || !flowData.difficulty || !flowData.language) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate that start video and node exist
    const videos = await getCollection('videos');
    const nodes = await getCollection('nodes');
    
    const startVideo = await videos.findOne({ _id: new ObjectId(flowData.startVideoId) });
    if (!startVideo) {
      return NextResponse.json(
        { error: 'Start video not found' },
        { status: 400 }
      );
    }

    const startNode = await nodes.findOne({ _id: new ObjectId(flowData.startNodeId) });
    if (!startNode) {
      return NextResponse.json(
        { error: 'Start node not found' },
        { status: 400 }
      );
    }

    const flows = await getCollection('flows');
    
    const newFlow = {
      ...flowData,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await flows.insertOne(newFlow);
    
    const createdFlow = await flows.findOne({ _id: result.insertedId });

    return NextResponse.json({
      success: true,
      data: createdFlow,
      message: 'Flow created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating flow:', error);
    return NextResponse.json(
      { error: 'Failed to create flow' },
      { status: 500 }
    );
  }
}