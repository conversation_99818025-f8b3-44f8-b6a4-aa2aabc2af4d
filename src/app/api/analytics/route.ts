import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getCollection } from '@/lib/mongodb';
import { AnalyticsData } from '@/types';

// GET /api/analytics - Get analytics data
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const timeframe = searchParams.get('timeframe') || '30d'; // 7d, 30d, 90d, 1y

    // Calculate date range
    const endDate = dateTo ? new Date(dateTo) : new Date();
    let startDate: Date;
    
    switch (timeframe) {
      case '7d':
        startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(endDate.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    if (dateFrom) {
      startDate = new Date(dateFrom);
    }

    const sessions = await getCollection('sessions');
    const events = await getCollection('events');
    const videos = await getCollection('videos');
    const flows = await getCollection('flows');

    // Get basic counts
    const [totalSessions, totalVideos, totalFlows] = await Promise.all([
      sessions.countDocuments({
        startTime: { $gte: startDate, $lte: endDate }
      }),
      videos.countDocuments({ isActive: true }),
      flows.countDocuments({ isActive: true })
    ]);

    // Get unique users count
    const uniqueUsers = await sessions.distinct('deviceId', {
      startTime: { $gte: startDate, $lte: endDate }
    });
    const totalUsers = uniqueUsers.length;

    // Calculate average session duration
    const sessionDurations = await sessions.aggregate([
      {
        $match: {
          startTime: { $gte: startDate, $lte: endDate },
          duration: { $exists: true, $gt: 0 }
        }
      },
      {
        $group: {
          _id: null,
          avgDuration: { $avg: '$duration' }
        }
      }
    ]).toArray();
    
    const averageSessionDuration = sessionDurations[0]?.avgDuration || 0;

    // Calculate completion rate
    const completedSessions = await sessions.countDocuments({
      startTime: { $gte: startDate, $lte: endDate },
      isCompleted: true
    });
    const completionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0;

    // Get popular videos
    const popularVideos = await events.aggregate([
      {
        $match: {
          type: 'video_start',
          timestamp: { $gte: startDate, $lte: endDate },
          videoId: { $exists: true }
        }
      },
      {
        $group: {
          _id: '$videoId',
          views: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'videos',
          localField: '_id',
          foreignField: '_id',
          as: 'video'
        }
      },
      {
        $unwind: '$video'
      },
      {
        $project: {
          videoId: '$_id',
          title: '$video.title',
          views: 1
        }
      },
      {
        $sort: { views: -1 }
      },
      {
        $limit: 10
      }
    ]).toArray();

    // Get user engagement over time
    const userEngagement = await sessions.aggregate([
      {
        $match: {
          startTime: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            date: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$startTime'
              }
            }
          },
          sessions: { $sum: 1 },
          users: { $addToSet: '$deviceId' }
        }
      },
      {
        $project: {
          date: '$_id.date',
          sessions: 1,
          users: { $size: '$users' }
        }
      },
      {
        $sort: { '_id.date': 1 }
      }
    ]).toArray();

    // Get device statistics
    const deviceStats = await sessions.aggregate([
      {
        $match: {
          startTime: { $gte: startDate, $lte: endDate },
          'deviceInfo.platform': { $exists: true }
        }
      },
      {
        $group: {
          _id: '$deviceInfo.platform',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          platform: '$_id',
          count: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalSessions] },
              100
            ]
          }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    // Get language statistics
    const languageStats = await sessions.aggregate([
      {
        $match: {
          startTime: { $gte: startDate, $lte: endDate },
          language: { $exists: true }
        }
      },
      {
        $group: {
          _id: '$language',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          language: '$_id',
          count: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalSessions] },
              100
            ]
          }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    const analyticsData: AnalyticsData = {
      totalSessions,
      totalUsers,
      totalVideos,
      totalFlows,
      averageSessionDuration: Math.round(averageSessionDuration),
      avgSessionDuration: Math.round(averageSessionDuration),
      completionRate: Math.round(completionRate * 100) / 100,
      sessionGrowth: 0, // Default value
      userGrowth: 0, // Default value
      peakUsageHour: 12, // Default value
      avgScore: 0, // Default value
      mostActiveDay: 'Monday', // Default value
      popularVideos: (popularVideos as Array<{ videoId: string; title: string; views: number }>).map((video) => ({
        videoId: video.videoId,
        title: video.title,
        views: video.views,
        completionRate: 0 // Default value since we don't calculate this yet
      })),
      userEngagement: userEngagement as Array<{ date: string; sessions: number; users: number }>,
      deviceStats: deviceStats as Array<{ platform: string; count: number; percentage: number }>,
      languageStats: languageStats as Array<{ language: string; count: number; percentage: number }>
    };

    return NextResponse.json({
      success: true,
      data: analyticsData
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}