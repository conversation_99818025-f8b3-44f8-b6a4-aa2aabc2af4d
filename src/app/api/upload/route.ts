import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions, getUserRole, hasRequiredRole } from '@/lib/auth';
import { 
  generatePresignedUploadUrl, 
  generateFileKey, 
  isValidVideoType, 
  isValidImageType 
} from '@/lib/cloudflare-r2';

// POST /api/upload - Generate presigned URL for file upload
export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called');
    const session = await getServerSession(authOptions);
    console.log('Session:', session ? 'Found' : 'Not found');
    if (!session) {
      console.log('No session found, returning 401');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const userRole = getUserRole(session);
    if (!userRole || !hasRequiredRole(userRole, ['admin', 'editor'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { fileName, fileType, fileSize, uploadType = 'video' } = body;
    console.log('Upload request body:', { fileName, fileType, fileSize, uploadType });

    // Validate required fields
    if (!fileName || !fileType) {
      return NextResponse.json(
        { error: 'fileName and fileType are required' },
        { status: 400 }
      );
    }

    // Validate file type based on upload type
    let isValidType = false;
    let prefix = '';

    switch (uploadType) {
      case 'video':
        isValidType = isValidVideoType(fileType);
        prefix = 'videos';
        break;
      case 'image':
      case 'thumbnail':
        isValidType = isValidImageType(fileType);
        prefix = 'images';
        break;
      case 'subtitle':
        isValidType = fileType === 'text/vtt' || fileType === 'application/x-subrip';
        prefix = 'subtitles';
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid upload type' },
          { status: 400 }
        );
    }

    if (!isValidType) {
      return NextResponse.json(
        { error: `Invalid file type for ${uploadType} upload` },
        { status: 400 }
      );
    }

    // Check file size limits (adjust as needed)
    const maxSizes = {
      video: 500 * 1024 * 1024, // 500MB
      image: 10 * 1024 * 1024,  // 10MB
      thumbnail: 5 * 1024 * 1024, // 5MB
      subtitle: 1 * 1024 * 1024,  // 1MB
    };

    if (fileSize && fileSize > maxSizes[uploadType as keyof typeof maxSizes]) {
      return NextResponse.json(
        { error: `File size exceeds limit for ${uploadType} uploads` },
        { status: 400 }
      );
    }

    // Generate unique file key
    const fileKey = generateFileKey(fileName, prefix);

    // Generate presigned URL
    console.log('Generating presigned URL for:', fileKey);
    const result = await generatePresignedUploadUrl(fileKey, fileType, 3600); // 1 hour expiry
    console.log('Presigned URL generated successfully:', result.uploadUrl.substring(0, 100) + '...');

    return NextResponse.json({
      success: true,
      data: {
        uploadUrl: result.uploadUrl,
        fileKey: result.key,
        publicUrl: result.publicUrl,
        expiresIn: 3600,
      }
    });

  } catch (error) {
    console.error('Error generating upload URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate upload URL' },
      { status: 500 }
    );
  }
}

// DELETE /api/upload - Delete a file from R2
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const userRole = getUserRole(session);
    if (!userRole || !hasRequiredRole(userRole, ['admin', 'editor'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const fileKey = searchParams.get('key');

    if (!fileKey) {
      return NextResponse.json(
        { error: 'File key is required' },
        { status: 400 }
      );
    }

    // Import deleteFile dynamically to avoid issues
    const { deleteFile } = await import('@/lib/cloudflare-r2');
    await deleteFile(fileKey);

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting file:', error);
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    );
  }
}
