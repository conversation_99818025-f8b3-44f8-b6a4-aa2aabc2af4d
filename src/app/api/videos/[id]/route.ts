import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { ObjectId } from 'mongodb';
import { authOptions } from '@/lib/auth';
import { getCollection } from '@/lib/mongodb';
import { UpdateVideoData } from '@/types';

// GET /api/videos/[id] - Get a specific video
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid video ID' }, { status: 400 });
    }

    const videos = await getCollection('videos');
    const video = await videos.findOne({ _id: new ObjectId(id) });

    if (!video) {
      return NextResponse.json({ error: 'Video not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: video
    });
  } catch (error) {
    console.error('Error fetching video:', error);
    return NextResponse.json(
      { error: 'Failed to fetch video' },
      { status: 500 }
    );
  }
}

// PUT /api/videos/[id] - Update a specific video
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session.user.role !== 'admin' && session.user.role !== 'editor') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid video ID' }, { status: 400 });
    }

    const body = await request.json();
    const updateData: UpdateVideoData = {
      title: body.title,
      titleAr: body.titleAr,
      description: body.description,
      descriptionAr: body.descriptionAr,
      videoUrls: body.videoUrls,
      thumbnailUrl: body.thumbnailUrl,
      duration: body.duration,
      fileSize: body.fileSize,
      language: body.language,
      category: body.category,
      tags: body.tags,
      isActive: body.isActive,
      metadata: body.metadata
    };

    // Remove undefined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateVideoData] === undefined) {
        delete updateData[key as keyof UpdateVideoData];
      }
    });

    const videos = await getCollection('videos');
    
    const result = await videos.updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: {
          ...updateData,
          updatedAt: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: 'Video not found' }, { status: 404 });
    }

    const updatedVideo = await videos.findOne({ _id: new ObjectId(id) });

    return NextResponse.json({
      success: true,
      data: updatedVideo,
      message: 'Video updated successfully'
    });
  } catch (error) {
    console.error('Error updating video:', error);
    return NextResponse.json(
      { error: 'Failed to update video' },
      { status: 500 }
    );
  }
}

// DELETE /api/videos/[id] - Delete a specific video
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid video ID' }, { status: 400 });
    }

    const videos = await getCollection('videos');
    const nodes = await getCollection('nodes');
    
    // Check if video is used in any nodes
    const relatedNodes = await nodes.countDocuments({ videoId: new ObjectId(id) });
    
    if (relatedNodes > 0) {
      return NextResponse.json(
        { error: 'Cannot delete video. It is used in existing flows.' },
        { status: 400 }
      );
    }

    const result = await videos.deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: 'Video not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Video deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting video:', error);
    return NextResponse.json(
      { error: 'Failed to delete video' },
      { status: 500 }
    );
  }
}