import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getDatabase } from '@/lib/mongodb';
import { DashboardStats, Session, Video } from '@/types';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get date range from query params (default to last 30 days)
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Connect to database
    const db = await getDatabase();
    
    // Fetch basic counts
    const [totalVideos, totalFlows, totalSessions, totalUsers] = await Promise.all([
      db.collection('videos').countDocuments(),
      db.collection('flows').countDocuments(),
      db.collection('sessions').countDocuments(),
      db.collection('users').countDocuments()
    ]);

    // Fetch recent sessions for activity
    const recentSessionsData = await db.collection('sessions')
      .find({
        startTime: { $gte: startDate }
      })
      .sort({ startTime: -1 })
      .limit(10)
      .toArray();

    const recentSessions = recentSessionsData as Session[];

    // Calculate average session duration
    const completedSessions = recentSessions.filter(s => s.endTime && s.startTime);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const avgDuration = completedSessions.length > 0
      ? completedSessions.reduce((sum, session) => {
          const duration = new Date(session.endTime!).getTime() - new Date(session.startTime).getTime();
          return sum + duration;
        }, 0) / completedSessions.length / 1000 // Convert to seconds
      : 0;

    // Calculate completion rate
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const completionRate = recentSessions.length > 0
      ? (recentSessions.filter(s => s.isCompleted).length / recentSessions.length) * 100
      : 0;

    // Fetch popular videos (mock data for now since we need aggregation)
    const videosListData = await db.collection('videos')
      .find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .toArray();

    const popularVideos = videosListData.map(video => ({
      video: video as Video,
      viewCount: Math.floor(Math.random() * 1000) + 50 // Mock data
    }));

    // Get system health (simplified)
    const systemHealth = {
      status: 'healthy' as const,
      uptime: Date.now() - (process.uptime() * 1000),
      lastCheck: new Date()
    };

    const stats: DashboardStats = {
      totalVideos,
      totalFlows,
      totalSessions,
      totalUsers,
      recentSessions,
      popularVideos,
      systemHealth
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}