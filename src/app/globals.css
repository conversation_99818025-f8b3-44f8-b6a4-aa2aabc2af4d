/* ===================================================================
   QUEST VR ADMIN PANEL - MODERN CSS FRAMEWORK
   ================================================================= */

/* ===================================================================
   1. CSS CUSTOM PROPERTIES (DESIGN TOKENS)
   ================================================================= */
:root {
  /* Enhanced Color Palette */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fefce8;
  --color-warning-100: #fef3c7;
  --color-warning-500: #eab308;
  --color-warning-600: #d97706;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  --color-purple-50: #faf5ff;
  --color-purple-100: #f3e8ff;
  --color-purple-500: #a855f7;
  --color-purple-600: #9333ea;
  --color-purple-700: #7c3aed;

  --color-indigo-50: #eef2ff;
  --color-indigo-100: #e0e7ff;
  --color-indigo-500: #6366f1;
  --color-indigo-600: #4f46e5;

  --color-emerald-50: #ecfdf5;
  --color-emerald-100: #d1fae5;
  --color-emerald-500: #10b981;
  --color-emerald-600: #059669;

  /* Typography */
  --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Enhanced Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glow-purple: 0 0 20px rgba(168, 85, 247, 0.3);
  --shadow-glow-emerald: 0 0 20px rgba(16, 185, 129, 0.3);

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-gray-500) 0%, var(--color-gray-600) 100%);
  --gradient-success: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
  --gradient-warning: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
  --gradient-error: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
  --gradient-purple: linear-gradient(135deg, var(--color-purple-500) 0%, var(--color-purple-600) 100%);
  --gradient-indigo: linear-gradient(135deg, var(--color-indigo-500) 0%, var(--color-indigo-600) 100%);
  --gradient-emerald: linear-gradient(135deg, var(--color-emerald-500) 0%, var(--color-emerald-600) 100%);
  --gradient-rainbow: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-sunset: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-cosmic: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===================================================================
   2. RESET AND BASE STYLES
   ================================================================= */

/* Ensure proper sidebar positioning */
.sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-fixed);
}
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--color-gray-800);
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.01em;
  position: relative;
}

a {
  color: inherit;
  text-decoration: none;
  transition: color var(--transition-fast);
}

button {
  font-family: inherit;
  cursor: pointer;
}

img {
  max-width: 100%;
  height: auto;
}

/* ===================================================================
   3. UTILITY CLASSES
   ================================================================= */

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

/* Flexbox */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

/* Gap */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }

/* Spacing - Margin */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-4 { margin: var(--space-4); }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-2 { margin-top: var(--space-2); margin-bottom: var(--space-2); }
.my-4 { margin-top: var(--space-4); margin-bottom: var(--space-4); }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.ml-2 { margin-left: var(--space-2); }
.mr-2 { margin-right: var(--space-2); }

/* Spacing - Padding */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.pt-2 { padding-top: var(--space-2); }
.pt-4 { padding-top: var(--space-4); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-4 { padding-bottom: var(--space-4); }
.pl-2 { padding-left: var(--space-2); }
.pr-2 { padding-right: var(--space-2); }

/* Width */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-4 { width: var(--space-4); }
.w-5 { width: 1.25rem; }
.w-6 { width: var(--space-6); }
.w-8 { width: var(--space-8); }
.w-12 { width: var(--space-12); }
.w-16 { width: var(--space-16); }
.w-20 { width: var(--space-20); }
.w-24 { width: var(--space-24); }
.w-32 { width: 8rem; }
.w-48 { width: 12rem; }
.w-64 { width: 16rem; }
.w-80 { width: 20rem; }
.w-96 { width: 24rem; }

/* Height */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-4 { height: var(--space-4); }
.h-5 { height: 1.25rem; }
.h-6 { height: var(--space-6); }
.h-8 { height: var(--space-8); }
.h-10 { height: 2.5rem; }
.h-12 { height: var(--space-12); }
.h-16 { height: var(--space-16); }
.h-20 { height: var(--space-20); }
.h-24 { height: var(--space-24); }
.h-32 { height: 8rem; }
.h-48 { height: 12rem; }
.h-64 { height: 16rem; }
.h-screen { height: 100vh; }
.min-h-screen { min-height: 100vh; }

/* Max Width */
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }

/* Position */
.static { position: static; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Positioning Values */
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-x-0 { left: 0; right: 0; }
.inset-y-0 { top: 0; bottom: 0; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.top-2 { top: var(--space-2); }
.right-2 { right: var(--space-2); }
.top-4 { top: var(--space-4); }
.right-4 { right: var(--space-4); }
.top-1\/2 { top: 50%; }
.left-1\/2 { left: 50%; }
.-translate-x-1\/2 { transform: translateX(-50%); }
.-translate-y-1\/2 { transform: translateY(-50%); }
.translate-center { transform: translate(-50%, -50%); }

/* Z-Index */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-dropdown { z-index: var(--z-dropdown); }
.z-sticky { z-index: var(--z-sticky); }
.z-fixed { z-index: var(--z-fixed); }
.z-modal-backdrop { z-index: var(--z-modal-backdrop); }
.z-modal { z-index: var(--z-modal); }
.z-popover { z-index: var(--z-popover); }
.z-tooltip { z-index: var(--z-tooltip); }

/* Colors - Background */
.bg-white { background-color: #ffffff; backdrop-filter: blur(10px); }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }
.bg-gray-200 { background-color: var(--color-gray-200); }
.bg-gray-300 { background-color: var(--color-gray-300); }
.bg-gray-400 { background-color: var(--color-gray-400); }
.bg-gray-500 { background-color: var(--color-gray-500); }
.bg-gray-600 { background-color: var(--color-gray-600); }
.bg-gray-700 { background-color: var(--color-gray-700); }
.bg-gray-800 { background-color: var(--color-gray-800); }
.bg-gray-900 { background-color: var(--color-gray-900); }
.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fee2e2; }
.bg-red-500 { background: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%); }
.bg-red-600 { background: linear-gradient(135deg, var(--color-error-600) 0%, #b91c1c 100%); }
.bg-blue-50 { background-color: var(--color-primary-50); }
.bg-blue-100 { background-color: var(--color-primary-100); }
.bg-blue-500 { background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%); }
.bg-blue-600 { background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%); }
.bg-green-50 { background-color: #f0fdf4; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-500 { background: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%); }
.bg-green-600 { background: linear-gradient(135deg, var(--color-success-600) 0%, #15803d 100%); }
.bg-yellow-50 { background-color: #fefce8; }
.bg-yellow-100 { background-color: #fef3c7; }
.bg-yellow-500 { background: linear-gradient(135deg, var(--color-warning-500) 0%, #d97706 100%); }
.bg-indigo-50 { background-color: #eef2ff; }
.bg-indigo-100 { background-color: #e0e7ff; }
.bg-indigo-500 { background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%); }
.bg-indigo-600 { background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%); }
.bg-purple-50 { background-color: #faf5ff; }
.bg-purple-100 { background-color: #f3e8ff; }
.bg-purple-500 { background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%); }
.bg-purple-600 { background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%); }

/* Glass Morphism Backgrounds */
.bg-glass { background: rgba(255, 255, 255, 0.25); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.18); }
.bg-glass-dark { background: rgba(0, 0, 0, 0.25); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); }

/* Colors - Text */
.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-400 { color: var(--color-gray-400); }
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-800 { color: var(--color-gray-800); }
.text-gray-900 { color: var(--color-gray-900); }
.text-red-500 { color: var(--color-error-500); }
.text-red-600 { color: var(--color-error-600); }
.text-red-700 { color: #b91c1c; }
.text-red-800 { color: #991b1b; }
.text-blue-500 { color: var(--color-primary-500); }
.text-blue-600 { color: var(--color-primary-600); }
.text-blue-700 { color: var(--color-primary-700); }
.text-blue-800 { color: #1e40af; }
.text-green-500 { color: var(--color-success-500); }
.text-green-600 { color: var(--color-success-600); }
.text-green-700 { color: #15803d; }
.text-green-800 { color: #166534; }
.text-yellow-600 { color: #ca8a04; }
.text-yellow-700 { color: #a16207; }
.text-yellow-800 { color: #854d0e; }
.text-indigo-600 { color: #4f46e5; }
.text-purple-600 { color: #9333ea; }

/* Border */
.border { border-width: 1px; border-style: solid; }
.border-0 { border-width: 0; }
.border-2 { border-width: 2px; border-style: solid; }
.border-t { border-top-width: 1px; border-top-style: solid; }
.border-b { border-bottom-width: 1px; border-bottom-style: solid; }
.border-l { border-left-width: 1px; border-left-style: solid; }
.border-r { border-right-width: 1px; border-right-style: solid; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-400 { border-color: #9ca3af; }
.border-red-300 { border-color: #fca5a5; }
.border-blue-300 { border-color: #93c5fd; }
.border-green-300 { border-color: #86efac; }
.border-dashed { border-style: dashed; }

/* Border Radius */
.rounded { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: 2rem; }
.rounded-full { border-radius: var(--radius-full); }
.rounded-l-none { border-top-left-radius: 0; border-bottom-left-radius: 0; }
.rounded-r-none { border-top-right-radius: 0; border-bottom-right-radius: 0; }

/* Organic Shapes */
.rounded-organic { border-radius: 2rem 1rem 2rem 1rem; }
.rounded-blob { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1.25; letter-spacing: 0.025em; }
.text-sm { font-size: 0.875rem; line-height: 1.5; letter-spacing: 0.01em; }
.text-base { font-size: 1rem; line-height: 1.6; letter-spacing: 0.01em; }
.text-lg { font-size: 1.125rem; line-height: 1.6; letter-spacing: 0.005em; }
.text-xl { font-size: 1.25rem; line-height: 1.6; letter-spacing: 0.005em; }
.text-2xl { font-size: 1.5rem; line-height: 1.5; letter-spacing: -0.01em; }
.text-3xl { font-size: 1.875rem; line-height: 1.4; letter-spacing: -0.015em; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

/* Aspect Ratio */
.aspect-square { aspect-ratio: 1 / 1; }
.aspect-video { aspect-ratio: 16 / 9; }
.aspect-auto { aspect-ratio: auto; }
.aspect-\[4\/3\] { aspect-ratio: 4 / 3; }
.aspect-\[3\/2\] { aspect-ratio: 3 / 2; }
.aspect-\[21\/9\] { aspect-ratio: 21 / 9; }

/* Object Fit */
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

/* Object Position */
.object-center { object-position: center; }
.object-top { object-position: top; }
.object-bottom { object-position: bottom; }
.object-left { object-position: left; }
.object-right { object-position: right; }

/* Cursor */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

/* User Select */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* Modern Filters */
.blur-none { filter: blur(0); }
.blur-sm { filter: blur(4px); }
.blur-md { filter: blur(8px); }
.blur-lg { filter: blur(16px); }
.brightness-50 { filter: brightness(0.5); }
.brightness-75 { filter: brightness(0.75); }
.brightness-100 { filter: brightness(1); }
.brightness-110 { filter: brightness(1.1); }
.brightness-125 { filter: brightness(1.25); }
.contrast-50 { filter: contrast(0.5); }
.contrast-100 { filter: contrast(1); }
.contrast-125 { filter: contrast(1.25); }
.grayscale { filter: grayscale(100%); }
.grayscale-0 { filter: grayscale(0); }
.sepia { filter: sepia(100%); }
.sepia-0 { filter: sepia(0); }

/* Backdrop Filters */
.backdrop-blur-none { backdrop-filter: blur(0); }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur-md { backdrop-filter: blur(8px); }
.backdrop-blur-lg { backdrop-filter: blur(16px); }
.backdrop-blur-xl { backdrop-filter: blur(24px); }
.backdrop-brightness-50 { backdrop-filter: brightness(0.5); }
.backdrop-brightness-75 { backdrop-filter: brightness(0.75); }
.backdrop-brightness-100 { backdrop-filter: brightness(1); }
.backdrop-brightness-110 { backdrop-filter: brightness(1.1); }
.backdrop-brightness-125 { backdrop-filter: brightness(1.25); }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* Shadows */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* Elevated Shadows */
.shadow-elevated { box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08); }
.shadow-floating { box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1); }
.shadow-glow { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1); }
.shadow-glow-blue { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1); }
.shadow-glow-purple { box-shadow: 0 0 20px rgba(147, 51, 234, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1); }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }

/* Transitions & Animations */
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-base);
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-base);
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-base);
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-base);
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-base);
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-base);
}
.transition-smooth { transition: all var(--transition-slow); }
.transition-bounce { transition: all var(--transition-slow) cubic-bezier(0.68, -0.55, 0.265, 1.55); }
.duration-200 { transition-duration: var(--transition-base); }
.duration-300 { transition-duration: var(--transition-slow); }

/* Modern Transforms */
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-102 { transform: scale(1.02); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }

/* Modern Keyframe Animations */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
}

/* Animation Classes */
.animate-fadeInUp { animation: fadeInUp 0.5s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.5s ease-out; }
.animate-scaleIn { animation: scaleIn 0.3s ease-out; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-glow { animation: glow 2s ease-in-out infinite; }
.animate-fade-in { animation: fadeIn 0.3s ease-in-out; }
.animate-slide-up { animation: slideUp 0.4s ease-out; }

/* Modern Interaction Utilities */
.hover-lift:hover {
  transform: translateY(-4px);
  transition: transform var(--transition-base);
}
.hover-scale:hover {
  transform: scale(1.05);
  transition: transform var(--transition-base);
}
.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  transition: box-shadow var(--transition-base);
}
.hover-rotate:hover {
  transform: rotate(5deg);
  transition: transform var(--transition-base);
}
.hover-brightness:hover {
  filter: brightness(1.1);
  transition: filter var(--transition-base);
}

/* Group Hover Effects */
.group:hover .group-hover\:scale-110 { transform: scale(1.1); }
.group:hover .group-hover\:opacity-100 { opacity: 1; }
.group:hover .group-hover\:translate-x-1 { transform: translateX(0.25rem); }

/* Hover States */
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-blue-50:hover { background-color: #eff6ff; }
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-red-600:hover { background-color: #dc2626; }
.hover\:bg-red-700:hover { background-color: #b91c1c; }
.hover\:bg-green-600:hover { background-color: #16a34a; }
.hover\:bg-green-700:hover { background-color: #15803d; }
.hover\:text-blue-600:hover { color: #2563eb; }
.hover\:text-blue-700:hover { color: #1d4ed8; }
.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:scale-105:hover { transform: scale(1.05); }

/* Focus States */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:ring-blue-500:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:ring-offset-2:focus { box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px rgba(59, 130, 246, 0.5); }
.focus\:border-blue-500:focus { border-color: var(--color-primary-500); }

/* Focus Ring Utility */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Disabled States */
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

/* ===================================================================
   5. RESPONSIVE DESIGN
   ================================================================= */

/* Small screens (640px and up) */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:hidden { display: none; }
  .sm\:px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
  .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\:flex-row { flex-direction: row; }
  .sm\:items-center { align-items: center; }
  .sm\:justify-between { justify-content: space-between; }
}

/* Medium screens (768px and up) */
@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }
  .md\:py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:flex-row { flex-direction: row; }
  .md\:items-center { align-items: center; }
  .md\:justify-between { justify-content: space-between; }
  .md\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
}

/* Large screens (1024px and up) */
@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:hidden { display: none; }
  .lg\:px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .lg\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .lg\:left-0 { left: 0; }
  .lg\:w-64 { width: 16rem; }
  .lg\:flex-col { flex-direction: column; }
  .lg\:fixed { position: fixed; }
  .lg\:inset-y-0 { top: 0; bottom: 0; }
  .lg\:z-50 { z-index: 50; }
  .lg\:pl-64 { padding-left: 16rem; }
  .lg\:pt-0 { padding-top: 0; }
}

/* Slate Colors */
.bg-slate-800 { background-color: #1e293b; }
.bg-slate-900 { background-color: #0f172a; }
.border-slate-700 { border-color: #334155; }
.text-slate-300 { color: #cbd5e1; }
.text-slate-400 { color: #94a3b8; }
.text-slate-500 { color: #64748b; }
.text-slate-600 { color: #475569; }
.text-slate-700 { color: #334155; }
.bg-slate-700 { background-color: #334155; }
.bg-slate-600 { background-color: #475569; }
.bg-slate-50 { background-color: #f8fafc; }

/* Gradient Backgrounds */
.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-slate-900 { --tw-gradient-from: #0f172a; --tw-gradient-to: rgba(15, 23, 42, 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.via-slate-800 { --tw-gradient-to: rgba(30, 41, 59, 0); --tw-gradient-stops: var(--tw-gradient-from), #1e293b, var(--tw-gradient-to); }
.to-slate-900 { --tw-gradient-to: #0f172a; }
.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-to: rgba(59, 130, 246, 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.via-purple-500 { --tw-gradient-to: rgba(168, 85, 247, 0); --tw-gradient-stops: var(--tw-gradient-from), #a855f7, var(--tw-gradient-to); }
.to-pink-500 { --tw-gradient-to: #ec4899; }
.to-purple-600 { --tw-gradient-to: #9333ea; }
.to-pink-600 { --tw-gradient-to: #db2777; }

/* Opacity Utilities */
.border-slate-700\/50 { border-color: rgba(51, 65, 85, 0.5); }
.bg-slate-700\/50 { background-color: rgba(51, 65, 85, 0.5); }
.bg-slate-600\/50 { background-color: rgba(71, 85, 105, 0.5); }
.bg-slate-800\/50 { background-color: rgba(30, 41, 59, 0.5); }
.bg-white\/20 { background-color: rgba(255, 255, 255, 0.2); }
.bg-blue-500\/20 { background-color: rgba(59, 130, 246, 0.2); }
.bg-purple-600\/20 { background-color: rgba(147, 51, 234, 0.2); }
.bg-red-500\/10 { background-color: rgba(239, 68, 68, 0.1); }
.shadow-blue-500\/25 { box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.25), 0 4px 6px -2px rgba(59, 130, 246, 0.25); }
.focus\:ring-blue-500\/50:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }

/* Animation Utilities */
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.group:hover .group-hover\:scale-110 { transform: scale(1.1); }
.group:hover .group-hover\:scale-105 { transform: scale(1.05); }

/* Backdrop Blur */
.backdrop-blur-sm { backdrop-filter: blur(4px); }

/* Border Radius */
.rounded-2xl { border-radius: 1rem; }
.rounded-xl { border-radius: 0.75rem; }

/* Text Utilities */
.tracking-wider { letter-spacing: 0.05em; }
.truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.min-w-0 { min-width: 0px; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.leading-relaxed { line-height: 1.625; }

/* Spacing Utilities */
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }

/* Padding Utilities */
.p-2\.5 { padding: 0.625rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.p-8 { padding: 2rem; }

/* Border Utilities */
.border-slate-200 { border-color: #e2e8f0; }
.border-slate-300 { border-color: #cbd5e1; }
.rounded-2xl { border-radius: 1rem; }

/* Focus Utilities */
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:ring-blue-500:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:ring-purple-500:focus { box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.5); }
.focus\:ring-green-500:focus { box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }

/* Background Utilities */
.bg-white { background-color: #ffffff; }
.bg-slate-50 { background-color: #f8fafc; }
.bg-purple-50 { background-color: #faf5ff; }
.bg-blue-50 { background-color: #eff6ff; }

/* Text Color Utilities */
.text-slate-700 { color: #334155; }
.text-purple-700 { color: #7c3aed; }
.text-blue-700 { color: #1d4ed8; }
.text-green-600 { color: #16a34a; }
.text-green-700 { color: #15803d; }

/* Additional Visual Effects */
.hover\:text-white:hover { color: #ffffff; }
.hover\:text-red-400:hover { color: #f87171; }
.hover\:bg-slate-700\/50:hover { background-color: rgba(51, 65, 85, 0.5); }
.hover\:bg-slate-600\/50:hover { background-color: rgba(71, 85, 105, 0.5); }
.hover\:bg-red-500\/10:hover { background-color: rgba(239, 68, 68, 0.1); }
.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:bg-slate-50:hover { background-color: #f8fafc; }
.hover\:border-slate-400:hover { border-color: #94a3b8; }

/* Transform Utilities */
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-105 { transform: scale(1.05); }

/* Transition Utilities */
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.-bottom-1 { bottom: -0.25rem; }
.-right-1 { right: -0.25rem; }
.-bottom-0\.5 { bottom: -0.125rem; }
.-right-0\.5 { right: -0.125rem; }

/* Size Utilities */
.w-2\.5 { width: 0.625rem; }
.h-2\.5 { height: 0.625rem; }
.w-3 { width: 0.75rem; }
.h-3 { height: 0.75rem; }
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }
.w-12 { width: 3rem; }
.h-12 { height: 3rem; }

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* ===================================================================
   6. ENHANCED COMPONENT STYLES
   ================================================================= */

/* Enhanced Card Components */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  overflow: hidden;
  transition: all var(--transition-slow);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  opacity: 0.6;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 2px 6px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.card-elevated {
  background: rgba(255, 255, 255, 0.98);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 1);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.card-interactive {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-interactive:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 20px rgba(0, 0, 0, 0.1),
    var(--shadow-glow);
}

.card-header {
  padding: var(--space-6) var(--space-8);
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  backdrop-filter: blur(10px);
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
}

.card-body {
  padding: var(--space-8);
}

.card-footer {
  padding: var(--space-6) var(--space-8);
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  backdrop-filter: blur(10px);
}

/* Enhanced Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 1.75rem;
  font-size: 0.875rem;
  font-weight: var(--font-weight-semibold);
  line-height: 1.5;
  border-radius: var(--radius-xl);
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  outline: none;
  min-height: 2.75rem;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn:focus {
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: #ffffff;
  box-shadow:
    0 4px 14px rgba(59, 130, 246, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: #ffffff;
  box-shadow:
    0 4px 14px rgba(100, 116, 139, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(100, 116, 139, 0.3);
}

.btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-gray-600) 0%, var(--color-gray-700) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(100, 116, 139, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-success {
  background: var(--gradient-success);
  color: #ffffff;
  box-shadow:
    0 4px 14px rgba(34, 197, 94, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-success-600) 0%, var(--color-success-700) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(34, 197, 94, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-danger {
  background: var(--gradient-error);
  color: #ffffff;
  box-shadow:
    0 4px 14px rgba(239, 68, 68, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-error-600) 0%, var(--color-error-700) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(239, 68, 68, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.9);
  color: var(--color-primary-600);
  border: 2px solid var(--color-primary-500);
  backdrop-filter: blur(10px);
  box-shadow:
    0 2px 8px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.btn-outline:hover:not(:disabled) {
  background: var(--gradient-primary);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-ghost {
  background: rgba(255, 255, 255, 0.6);
  color: var(--color-gray-600);
  border: 1px solid rgba(100, 116, 139, 0.2);
  backdrop-filter: blur(10px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.btn-ghost:hover:not(:disabled) {
  background: rgba(100, 116, 139, 0.1);
  color: var(--color-gray-700);
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.btn-sm {
  padding: 0.625rem 1.25rem;
  font-size: 0.75rem;
  line-height: 1.25;
  border-radius: var(--radius-lg);
  min-height: 2.25rem;
  gap: 0.375rem;
}

.btn-lg {
  padding: 1.125rem 2.25rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--radius-2xl);
  min-height: 3.25rem;
  gap: 0.625rem;
}

/* Enhanced Form Components */
.form-input {
  display: block;
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-gray-800);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: var(--radius-xl);
  transition: all var(--transition-base);
  font-weight: var(--font-weight-medium);
  outline: none;
  letter-spacing: 0.01em;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.form-input::placeholder {
  color: var(--color-gray-400);
  font-weight: var(--font-weight-normal);
}

.form-input:focus {
  border-color: var(--color-primary-500);
  background: rgba(255, 255, 255, 0.98);
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.form-input:hover:not(:focus) {
  border-color: rgba(226, 232, 240, 1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
  letter-spacing: 0.025em;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
}

.form-error {
  color: var(--color-error-600);
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-1);
  letter-spacing: 0.025em;
}

/* Enhanced Navigation Components */
.nav-link {
  display: flex;
  align-items: center;
  padding: 0.875rem 1.5rem;
  color: #64748b;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  gap: 0.75rem;
  line-height: 1.5;
  backdrop-filter: blur(10px);
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-link:hover {
  color: #334155;
  background: rgba(248, 250, 252, 0.9);
  backdrop-filter: blur(15px);
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.nav-link:hover::before {
  opacity: 1;
}

.nav-link-active {
  color: #ffffff;
  background: var(--gradient-primary);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.nav-link-active::before {
  opacity: 0;
}

.nav-link-inactive {
  color: #64748b;
}

.nav-link-inactive:hover {
  color: #334155;
  background: rgba(248, 250, 252, 0.9);
  backdrop-filter: blur(15px);
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Enhanced Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.875rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-full);
  letter-spacing: 0.025em;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.status-published {
  background: linear-gradient(135deg, var(--color-success-50) 0%, var(--color-success-100) 100%);
  color: var(--color-success-700);
  border-color: rgba(34, 197, 94, 0.2);
}

.status-draft {
  background: linear-gradient(135deg, var(--color-warning-50) 0%, var(--color-warning-100) 100%);
  color: var(--color-warning-600);
  border-color: rgba(234, 179, 8, 0.2);
}

.status-archived {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  color: var(--color-gray-600);
  border-color: rgba(100, 116, 139, 0.2);
}

.status-active {
  background: linear-gradient(135deg, var(--color-emerald-50) 0%, var(--color-emerald-100) 100%);
  color: var(--color-emerald-600);
  border-color: rgba(16, 185, 129, 0.2);
}

.status-inactive {
  background: linear-gradient(135deg, var(--color-error-50) 0%, var(--color-error-100) 100%);
  color: var(--color-error-600);
  border-color: rgba(239, 68, 68, 0.2);
}

.status-pending {
  background: linear-gradient(135deg, var(--color-indigo-50) 0%, var(--color-indigo-100) 100%);
  color: var(--color-indigo-600);
  border-color: rgba(99, 102, 241, 0.2);
}

.status-warning {
  background: linear-gradient(135deg, var(--color-warning-50) 0%, var(--color-warning-100) 100%);
  color: var(--color-warning-600);
  border-color: rgba(234, 179, 8, 0.2);
}

/* Enhanced Layout Components */
.app-container {
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.08) 0%, transparent 50%);
}

.app-content {
  padding: var(--space-8);
  max-width: 100%;
}

.app-header {
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Modern Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-bold);
}

.gradient-text-purple {
  background: var(--gradient-purple);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-bold);
}

.gradient-text-emerald {
  background: var(--gradient-emerald);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-bold);
}

/* Enhanced Icon Utilities */
.icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.icon-sm {
  width: 1rem;
  height: 1rem;
}

.icon-lg {
  width: 1.5rem;
  height: 1.5rem;
}

.icon-xl {
  width: 2rem;
  height: 2rem;
}

/* Modern Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Enhanced Focus States */
.focus-ring:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

.focus-ring-purple:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(168, 85, 247, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

.focus-ring-emerald:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(16, 185, 129, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Modern Dividers */
.divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(226, 232, 240, 0.8), transparent);
  margin: var(--space-6) 0;
}

.divider-vertical {
  width: 1px;
  background: linear-gradient(180deg, transparent, rgba(226, 232, 240, 0.8), transparent);
  margin: 0 var(--space-4);
}

/* Enhanced Spacing Utilities */
.space-y-1 > * + * { margin-top: var(--space-1); }
.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-3 > * + * { margin-top: var(--space-3); }
.space-y-4 > * + * { margin-top: var(--space-4); }
.space-y-6 > * + * { margin-top: var(--space-6); }
.space-y-8 > * + * { margin-top: var(--space-8); }

.space-x-1 > * + * { margin-left: var(--space-1); }
.space-x-2 > * + * { margin-left: var(--space-2); }
.space-x-3 > * + * { margin-left: var(--space-3); }
.space-x-4 > * + * { margin-left: var(--space-4); }
.space-x-6 > * + * { margin-left: var(--space-6); }
.space-x-8 > * + * { margin-left: var(--space-8); }
