'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AppLayout } from '@/components/AppLayout';
import {
  ArrowLeftIcon,
  VideoCameraIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';
import Link from 'next/link';

const videoSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description must be less than 1000 characters'),
  videoFile: z.any().optional(), // File will be handled separately
  duration: z.number().min(1, 'Duration must be greater than 0'),
  language: z.string().min(1, 'Language is required'),
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive']),
  thumbnailUrl: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
    message: 'Invalid thumbnail URL'
  }),
  subtitles: z.array(z.object({
    language: z.string(),
    url: z.string().url(),
  })).optional(),
});

type NewVideoFormData = z.infer<typeof videoSchema>;

export default function NewVideoPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [selectedVideoFile, setSelectedVideoFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedVideoUrl, setUploadedVideoUrl] = useState<string>('');
  const [generatedThumbnail, setGeneratedThumbnail] = useState<string>('');
  const [isGeneratingThumbnail, setIsGeneratingThumbnail] = useState(false);
  const [uploadedThumbnailUrl, setUploadedThumbnailUrl] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<NewVideoFormData>({
    resolver: zodResolver(videoSchema),
    defaultValues: {
      status: 'active',
      language: 'en',
      tags: [],
    },
  });

  // const watchedTags = watch('tags') || [];

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      setValue('tags', newTags);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    setValue('tags', newTags);
  };

  // Generate thumbnail preview for display
  const generateThumbnailPreview = async (videoFile: File) => {
    setIsGeneratingThumbnail(true);
    try {
      const thumbnailBlob = await generateThumbnail(videoFile);
      const thumbnailUrl = URL.createObjectURL(thumbnailBlob);
      setGeneratedThumbnail(thumbnailUrl);
    } catch (error) {
      console.error('Failed to generate thumbnail preview:', error);
      toast.error('Failed to generate thumbnail preview');
    } finally {
      setIsGeneratingThumbnail(false);
    }
  };

  // Generate thumbnail from video
  const generateThumbnail = (videoFile: File): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.onloadedmetadata = () => {
        // Set canvas dimensions (limit max size for performance)
        const maxWidth = 800;
        const maxHeight = 600;
        let { videoWidth, videoHeight } = video;

        if (videoWidth > maxWidth) {
          videoHeight = (videoHeight * maxWidth) / videoWidth;
          videoWidth = maxWidth;
        }
        if (videoHeight > maxHeight) {
          videoWidth = (videoWidth * maxHeight) / videoHeight;
          videoHeight = maxHeight;
        }

        canvas.width = videoWidth;
        canvas.height = videoHeight;

        // Seek to 10% of video duration for thumbnail
        video.currentTime = video.duration * 0.1;
      };

      video.onseeked = () => {
        if (ctx) {
          // Draw video frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Convert canvas to blob
          canvas.toBlob((blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to generate thumbnail'));
            }
          }, 'image/jpeg', 0.8);
        } else {
          reject(new Error('Canvas context not available'));
        }

        // Clean up
        window.URL.revokeObjectURL(video.src);
      };

      video.onerror = () => {
        reject(new Error('Failed to load video for thumbnail generation'));
        window.URL.revokeObjectURL(video.src);
      };

      video.src = URL.createObjectURL(videoFile);
    });
  };

  // Handle video file selection
  const handleVideoFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const validTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];
      if (!validTypes.includes(file.type)) {
        toast.error('Please select a valid video file (MP4, WebM, OGG, AVI, MOV)');
        return;
      }

      // Validate file size (500MB limit)
      const maxSize = 500 * 1024 * 1024; // 500MB
      if (file.size > maxSize) {
        toast.error('File size must be less than 500MB');
        return;
      }

      setSelectedVideoFile(file);

      // Auto-populate duration and generate thumbnail preview
      const video = document.createElement('video');
      video.preload = 'metadata';
      video.onloadedmetadata = () => {
        setValue('duration', Math.round(video.duration));
        window.URL.revokeObjectURL(video.src);

        // Generate thumbnail preview
        generateThumbnailPreview(file);
      };
      video.src = URL.createObjectURL(file);
    }
  };

  // Upload thumbnail to R2
  const uploadThumbnail = async (thumbnailBlob: Blob, originalFileName: string): Promise<string> => {
    try {
      // Create a filename for the thumbnail
      const thumbnailFileName = `${originalFileName.split('.')[0]}_thumbnail.jpg`;

      console.log('Uploading thumbnail:', { fileName: thumbnailFileName, size: thumbnailBlob.size });
      console.log('Current session:', session);

      // Get presigned upload URL for thumbnail
      console.log('Making request to /api/upload...');
      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          fileName: thumbnailFileName,
          fileType: 'image/jpeg',
          fileSize: thumbnailBlob.size,
          uploadType: 'thumbnail',
        }),
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        console.error('Upload API error:', { status: uploadResponse.status, statusText: uploadResponse.statusText, errorText });
        throw new Error(`Failed to get thumbnail upload URL: ${uploadResponse.status} ${errorText}`);
      }

      const responseData = await uploadResponse.json();
      const { data } = responseData;
      if (!data || !data.uploadUrl || !data.publicUrl) {
        throw new Error('Invalid response from upload API');
      }

      const { uploadUrl, publicUrl } = data;

      // Upload thumbnail to R2
      const r2Response = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'image/jpeg',
        },
        body: thumbnailBlob,
      });

      if (!r2Response.ok) {
        const r2ErrorText = await r2Response.text();
        throw new Error(`Failed to upload thumbnail to R2: ${r2Response.status} ${r2ErrorText}`);
      }
      return publicUrl;
    } catch (error) {
      console.error('Thumbnail upload error:', error);

      // Check if it's a network error
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        console.error('Network error - check if the server is running and accessible');
        throw new Error('Network error: Unable to connect to the upload service. Please check your connection and try again.');
      }

      throw error;
    }
  };

  // Upload video file to R2
  const uploadVideoFile = async (file: File): Promise<{ videoUrl: string; thumbnailUrl: string }> => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Generate thumbnail first
      let thumbnailUrl = '';
      try {
        const thumbnailBlob = await generateThumbnail(file);
        // Upload thumbnail
        thumbnailUrl = await uploadThumbnail(thumbnailBlob, file.name);
      } catch (thumbnailError) {
        console.warn('Thumbnail upload failed, continuing with video upload:', thumbnailError);
        // Continue with video upload even if thumbnail fails
      }

      // Get presigned upload URL for video
      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          uploadType: 'video',
        }),
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        throw new Error(`Failed to get video upload URL: ${uploadResponse.status} ${errorText}`);
      }

      const { data } = await uploadResponse.json();
      const { uploadUrl, publicUrl } = data;

      // Upload video file to R2 using presigned URL
      const xhr = new XMLHttpRequest();

      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            const progress = Math.round((e.loaded / e.total) * 100);
            setUploadProgress(progress);
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            setUploadedVideoUrl(publicUrl);
            setUploadedThumbnailUrl(thumbnailUrl);
            resolve({ videoUrl: publicUrl, thumbnailUrl });
          } else {
            reject(new Error('Upload failed'));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed'));
        });

        xhr.open('PUT', uploadUrl);
        xhr.setRequestHeader('Content-Type', file.type);
        xhr.send(file);
      });
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const onSubmit = async (data: NewVideoFormData) => {
    setIsSubmitting(true);
    try {
      // First, upload the video file if one is selected
      let videoUrl = uploadedVideoUrl;
      let thumbnailUrl = uploadedThumbnailUrl || data.thumbnailUrl || ''; // Prioritize uploaded thumbnail

      if (selectedVideoFile && !uploadedVideoUrl) {
        const uploadToast = toast.loading('Uploading video and generating thumbnail...');
        try {
          const uploadResult = await uploadVideoFile(selectedVideoFile);
          videoUrl = uploadResult.videoUrl;
          thumbnailUrl = uploadResult.thumbnailUrl; // Use auto-generated thumbnail
          toast.dismiss(uploadToast);
          toast.success('Video and thumbnail uploaded successfully!');
        } catch (error) {
          toast.dismiss(uploadToast);
          throw error;
        }
      }

      if (!videoUrl) {
        throw new Error('Please upload a video file');
      }

      // Create video record with R2 URL and auto-generated thumbnail
      const videoData = {
        title: data.title,
        description: data.description,
        videoUrls: {
          original: videoUrl,
        },
        duration: data.duration,
        language: data.language,
        category: data.category,
        tags,
        isActive: data.status === 'active',
        status: 'draft' as const,
        thumbnailUrl: thumbnailUrl || undefined, // Only include if not empty
        r2FileKey: videoUrl.split('/').pop(), // Extract file key from URL
        processingStatus: 'completed' as const,
        metadata: {
          originalFileName: selectedVideoFile?.name,
        },
      };

      const response = await fetch('/api/videos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(videoData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create video');
      }

      const result = await response.json();
      toast.success('Video created successfully!');
      router.push(`/videos/${result.data._id}`);
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : 'Failed to create video');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  return (
    <AppLayout>
      <div className="app-content">
        {/* Header */}
        <div className="app-header">
          <div className="flex items-center space-x-4">
            <Link
              href="/videos"
              className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
            >
              <ArrowLeftIcon className="icon mr-1" />
              Back to Videos
            </Link>
          </div>
          <h1 className="mt-4 text-3xl font-bold text-gray-900">Add New Video</h1>
          <p className="mt-2 text-sm text-gray-600">
            Upload and configure a new VR training video
          </p>
        </div>

        {/* Form */}
        <div className="card">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 gap-6">
                <div className="form-group">
                  <label htmlFor="title" className="form-label">
                    Title *
                  </label>
                  <input
                    {...register('title')}
                    type="text"
                    className="form-input"
                    placeholder="Enter video title"
                  />
                  {errors.title && (
                    <p className="form-error">{errors.title.message}</p>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="description" className="form-label">
                    Description *
                  </label>
                  <textarea
                    {...register('description')}
                    rows={4}
                    className="form-textarea"
                    placeholder="Enter video description"
                  />
                  {errors.description && (
                    <p className="form-error">{errors.description.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="form-group">
                    <label htmlFor="language" className="form-label">
                      Language *
                    </label>
                    <select
                      {...register('language')}
                      className="form-select"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                      <option value="it">Italian</option>
                      <option value="pt">Portuguese</option>
                    </select>
                    {errors.language && (
                      <p className="form-error">{errors.language.message}</p>
                    )}
                  </div>

                  <div className="form-group">
                    <label htmlFor="category" className="form-label">
                      Category *
                    </label>
                    <select
                      {...register('category')}
                      className="form-select"
                    >
                      <option value="">Select a category</option>
                      <option value="safety">Safety Training</option>
                      <option value="technical">Technical Skills</option>
                      <option value="soft-skills">Soft Skills</option>
                      <option value="compliance">Compliance</option>
                      <option value="onboarding">Onboarding</option>
                      <option value="product">Product Training</option>
                    </select>
                    {errors.category && (
                      <p className="form-error">{errors.category.message}</p>
                    )}
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="status" className="form-label">
                    Status *
                  </label>
                  <select
                    {...register('status')}
                    className="form-select"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                  {errors.status && (
                    <p className="form-error">{errors.status.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Video Upload */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Video Upload</h3>
              <div className="grid grid-cols-1 gap-6">
                <div className="form-group">
                  <label htmlFor="videoFile" className="form-label">
                    Video File *
                  </label>
                  <div className="relative">
                    <input
                      type="file"
                      id="videoFile"
                      accept="video/*"
                      onChange={handleVideoFileChange}
                      className="form-input file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                    {selectedVideoFile && (
                      <div className="mt-2 text-sm text-gray-600">
                        Selected: {selectedVideoFile.name} ({(selectedVideoFile.size / 1024 / 1024).toFixed(2)} MB)
                      </div>
                    )}
                    {uploadedVideoUrl && (
                      <div className="mt-2 text-sm text-green-600">
                        ✓ Video uploaded successfully
                      </div>
                    )}
                    {uploadedThumbnailUrl && (
                      <div className="mt-2 text-sm text-green-600">
                        ✓ Thumbnail uploaded successfully
                      </div>
                    )}
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    Upload your video file (MP4, WebM, OGG, AVI, MOV). Maximum size: 500MB
                  </p>

                  {/* Upload Progress */}
                  {isUploading && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Uploading...</span>
                        <span>{uploadProgress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Thumbnail Preview */}
                  {(generatedThumbnail || isGeneratingThumbnail) && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Auto-Generated Thumbnail</h4>
                      <div className="relative">
                        {isGeneratingThumbnail ? (
                          <div className="w-32 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                          </div>
                        ) : generatedThumbnail ? (
                          <div className="relative">
                            <img
                              src={generatedThumbnail}
                              alt="Auto-generated thumbnail"
                              className="w-32 h-20 object-cover rounded-lg border border-gray-300 shadow-sm"
                            />
                            <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                              ✓ Auto
                            </div>
                          </div>
                        ) : null}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        This thumbnail will be automatically uploaded with your video
                      </p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="form-group">
                    <label htmlFor="duration" className="form-label">
                      Duration (seconds) *
                    </label>
                    <input
                      {...register('duration', { valueAsNumber: true })}
                      type="number"
                      min="1"
                      className="form-input"
                      placeholder="300"
                    />
                    {errors.duration && (
                      <p className="form-error">{errors.duration.message}</p>
                    )}
                  </div>

                  <div className="form-group">
                    <label htmlFor="thumbnailUrl" className="form-label">
                      Custom Thumbnail URL (Optional)
                    </label>
                    <input
                      {...register('thumbnailUrl')}
                      type="url"
                      className="form-input"
                      placeholder="https://example.com/thumbnail.jpg"
                    />
                    {errors.thumbnailUrl && (
                      <p className="form-error">{errors.thumbnailUrl.message}</p>
                    )}
                    <p className="mt-1 text-sm text-gray-500">
                      Leave empty to use auto-generated thumbnail from video
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
              <div className="form-group">
                <label htmlFor="tags" className="form-label">
                  Add Tags
                </label>
                <div className="flex">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    className="form-input rounded-r-none"
                    placeholder="Enter a tag"
                  />
                  <button
                    type="button"
                    onClick={addTag}
                    className="btn-secondary rounded-l-none"
                  >
                    Add
                  </button>
                </div>
                {tags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Submit */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex justify-end space-x-3">
                <Link
                  href="/videos"
                  className="btn-secondary"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <VideoCameraIcon className="icon-sm mr-2" />
                      Create Video
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  );
}