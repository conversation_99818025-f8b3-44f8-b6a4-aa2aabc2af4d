'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Link from 'next/link';
import { Video } from '@/types';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AppLayout } from '@/components/AppLayout';
import {
  ArrowLeftIcon,
  VideoCameraIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const editVideoSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description must be less than 1000 characters'),
  duration: z.number().min(1, 'Duration must be greater than 0'),
  language: z.string().min(1, 'Language is required'),
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive']),
});

type EditVideoFormData = z.infer<typeof editVideoSchema>;

export default function EditVideoPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const [video, setVideo] = useState<Video | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  const videoId = params.id as string;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<EditVideoFormData>({
    resolver: zodResolver(editVideoSchema),
  });

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }
    if (status === 'authenticated' && videoId) {
      fetchVideo();
    }
  }, [status, videoId, router]);

  const fetchVideo = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/videos/${videoId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Video not found');
          router.push('/videos');
          return;
        }
        throw new Error('Failed to fetch video');
      }

      const data = await response.json();
      const videoData = data.data;
      setVideo(videoData);
      
      // Populate form with existing data
      reset({
        title: videoData.title || '',
        description: videoData.description || '',
        duration: videoData.duration || 0,
        language: videoData.language || 'en',
        category: videoData.category || '',
        status: videoData.status || 'active',
      });
      
      // Set tags
      if (videoData.tags) {
        setTags(videoData.tags);
        setValue('tags', videoData.tags);
      }
    } catch (error) {
      console.error('Error fetching video:', error);
      toast.error('Failed to load video');
      router.push('/videos');
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      setValue('tags', newTags);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    setValue('tags', newTags);
  };

  const onSubmit = async (data: EditVideoFormData) => {
    try {
      setIsSubmitting(true);

      const updateData = {
        ...data,
        tags,
      };

      const response = await fetch(`/api/videos/${videoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update video');
      }

      toast.success('Video updated successfully');
      router.push(`/videos/${videoId}`);
    } catch (error) {
      console.error('Error updating video:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update video');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-screen">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    );
  }

  if (!video) {
    return (
      <AppLayout>
        <div className="app-content">
          <div className="text-center py-12">
            <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Video not found</h3>
            <p className="mt-1 text-sm text-gray-500">
              The video you're trying to edit doesn't exist or has been deleted.
            </p>
            <div className="mt-6">
              <Link href="/videos" className="btn-primary">
                <ArrowLeftIcon className="icon-sm mr-2" />
                Back to Videos
              </Link>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Check permissions
  if (session?.user?.role !== 'admin' && session?.user?.role !== 'editor') {
    return (
      <AppLayout>
        <div className="app-content">
          <div className="text-center py-12">
            <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
            <p className="mt-1 text-sm text-gray-500">
              You don't have permission to edit videos.
            </p>
            <div className="mt-6">
              <Link href="/videos" className="btn-primary">
                <ArrowLeftIcon className="icon-sm mr-2" />
                Back to Videos
              </Link>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="app-content">
        {/* Header */}
        <div className="app-header">
          <div className="flex items-center space-x-4">
            <Link
              href={`/videos/${videoId}`}
              className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
            >
              <ArrowLeftIcon className="icon mr-1" />
              Back to Video
            </Link>
          </div>
          <h1 className="mt-4 text-3xl font-bold text-gray-900">Edit Video</h1>
          <p className="mt-2 text-lg text-gray-600">
            Update the video information and settings
          </p>
        </div>

        {/* Form */}
        <div className="card">
          <form onSubmit={handleSubmit(onSubmit)} className="card-body space-y-6">
            {/* Basic Information */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              {/* Title */}
              <div>
                <label htmlFor="title" className="form-label">
                  Title *
                </label>
                <input
                  type="text"
                  id="title"
                  {...register('title')}
                  className="form-input"
                  placeholder="Enter video title"
                />
                {errors.title && (
                  <p className="form-error">{errors.title.message}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="form-label">
                  Description *
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register('description')}
                  className="form-input"
                  placeholder="Enter video description"
                />
                {errors.description && (
                  <p className="form-error">{errors.description.message}</p>
                )}
              </div>

              {/* Duration, Language, Category */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="duration" className="form-label">
                    Duration (seconds) *
                  </label>
                  <input
                    type="number"
                    id="duration"
                    {...register('duration', { valueAsNumber: true })}
                    className="form-input"
                    placeholder="0"
                    min="1"
                  />
                  {errors.duration && (
                    <p className="form-error">{errors.duration.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="language" className="form-label">
                    Language *
                  </label>
                  <select
                    id="language"
                    {...register('language')}
                    className="form-input"
                  >
                    <option value="">Select language</option>
                    <option value="en">English</option>
                    <option value="ar">Arabic</option>
                    <option value="fr">French</option>
                    <option value="es">Spanish</option>
                  </select>
                  {errors.language && (
                    <p className="form-error">{errors.language.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="category" className="form-label">
                    Category *
                  </label>
                  <select
                    id="category"
                    {...register('category')}
                    className="form-input"
                  >
                    <option value="">Select category</option>
                    <option value="training">Training</option>
                    <option value="safety">Safety</option>
                    <option value="onboarding">Onboarding</option>
                    <option value="skills">Skills Development</option>
                    <option value="compliance">Compliance</option>
                  </select>
                  {errors.category && (
                    <p className="form-error">{errors.category.message}</p>
                  )}
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="form-label">Tags</label>
                <div className="space-y-3">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      className="form-input flex-1"
                      placeholder="Add a tag"
                    />
                    <button
                      type="button"
                      onClick={addTag}
                      className="btn-secondary"
                    >
                      Add
                    </button>
                  </div>
                  {tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-2 text-blue-600 hover:text-blue-800"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Status */}
              <div>
                <label htmlFor="status" className="form-label">
                  Status *
                </label>
                <select
                  id="status"
                  {...register('status')}
                  className="form-input"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
                {errors.status && (
                  <p className="form-error">{errors.status.message}</p>
                )}
              </div>
            </div>

            {/* Submit */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex justify-end space-x-3">
                <Link
                  href={`/videos/${videoId}`}
                  className="btn-secondary"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <VideoCameraIcon className="icon-sm mr-2" />
                      Update Video
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  );
}
