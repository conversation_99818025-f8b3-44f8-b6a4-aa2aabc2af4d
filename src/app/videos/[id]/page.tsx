'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Video } from '@/types';
import { formatDuration, formatDate } from '@/lib/utils';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AppLayout } from '@/components/AppLayout';
import {
  ArrowLeftIcon,
  PlayIcon,
  PencilIcon,
  TrashIcon,
  VideoCameraIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function VideoDetailPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const [video, setVideo] = useState<Video | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);

  const videoId = params.id as string;

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }
    if (status === 'authenticated' && videoId) {
      fetchVideo();
    }
  }, [status, videoId, router]);

  const fetchVideo = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/videos/${videoId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Video not found');
          router.push('/videos');
          return;
        }
        throw new Error('Failed to fetch video');
      }

      const data = await response.json();
      setVideo(data.data);
    } catch (error) {
      console.error('Error fetching video:', error);
      toast.error('Failed to load video');
      router.push('/videos');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!video || !confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleting(true);
      const response = await fetch(`/api/videos/${videoId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete video');
      }

      toast.success('Video deleted successfully');
      router.push('/videos');
    } catch (error) {
      console.error('Error deleting video:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete video');
    } finally {
      setDeleting(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-screen">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    );
  }

  if (!video) {
    return (
      <AppLayout>
        <div className="app-content">
          <div className="text-center py-12">
            <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Video not found</h3>
            <p className="mt-1 text-sm text-gray-500">
              The video you're looking for doesn't exist or has been deleted.
            </p>
            <div className="mt-6">
              <Link href="/videos" className="btn-primary">
                <ArrowLeftIcon className="icon-sm mr-2" />
                Back to Videos
              </Link>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="app-content">
        {/* Header */}
        <div className="app-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/videos"
                className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                <ArrowLeftIcon className="icon mr-1" />
                Back to Videos
              </Link>
            </div>
            <div className="flex items-center space-x-3">
              {(session?.user?.role === 'admin' || session?.user?.role === 'editor') && (
                <>
                  <Link
                    href={`/videos/${videoId}/edit`}
                    className="btn-secondary"
                  >
                    <PencilIcon className="icon-sm mr-2" />
                    Edit
                  </Link>
                  {session?.user?.role === 'admin' && (
                    <button
                      onClick={handleDelete}
                      disabled={deleting}
                      className="btn-danger disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {deleting ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Deleting...
                        </>
                      ) : (
                        <>
                          <TrashIcon className="icon-sm mr-2" />
                          Delete
                        </>
                      )}
                    </button>
                  )}
                </>
              )}
            </div>
          </div>
          <h1 className="mt-4 text-3xl font-bold text-gray-900">{video.title}</h1>
          {video.description && (
            <p className="mt-2 text-lg text-gray-600">{video.description}</p>
          )}
        </div>

        {/* Video Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Video Player */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="relative aspect-video bg-gray-900 rounded-lg overflow-hidden">
                {video.videoUrls && video.videoUrls.length > 0 ? (
                  <video
                    controls
                    className="w-full h-full"
                    poster={video.thumbnailUrl}
                  >
                    <source src={video.videoUrls[0]} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                ) : video.thumbnailUrl ? (
                  <div className="relative w-full h-full">
                    <Image
                      src={video.thumbnailUrl}
                      alt={video.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                        <PlayIcon className="w-8 h-8 text-white ml-1" />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-500">No video available</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Video Details */}
          <div className="space-y-6">
            {/* Basic Info */}
            <div className="card">
              <div className="card-body">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Video Details</h3>
                <dl className="space-y-3">
                  {video.duration && (
                    <div className="flex items-center">
                      <dt className="flex items-center text-sm font-medium text-gray-500 w-24">
                        <ClockIcon className="icon-sm mr-2" />
                        Duration
                      </dt>
                      <dd className="text-sm text-gray-900">{formatDuration(video.duration)}</dd>
                    </div>
                  )}
                  {video.language && (
                    <div className="flex items-center">
                      <dt className="flex items-center text-sm font-medium text-gray-500 w-24">
                        <GlobeAltIcon className="icon-sm mr-2" />
                        Language
                      </dt>
                      <dd className="text-sm text-gray-900">{video.language.toUpperCase()}</dd>
                    </div>
                  )}
                  {video.category && (
                    <div className="flex items-center">
                      <dt className="flex items-center text-sm font-medium text-gray-500 w-24">
                        <TagIcon className="icon-sm mr-2" />
                        Category
                      </dt>
                      <dd className="text-sm text-gray-900">{video.category}</dd>
                    </div>
                  )}
                  <div className="flex items-center">
                    <dt className="flex items-center text-sm font-medium text-gray-500 w-24">
                      <CalendarIcon className="icon-sm mr-2" />
                      Created
                    </dt>
                    <dd className="text-sm text-gray-900">
                      {formatDate(video.createdAt || new Date())}
                    </dd>
                  </div>
                  <div className="flex items-center">
                    <dt className="text-sm font-medium text-gray-500 w-24">Status</dt>
                    <dd>
                      <span className={
                        video.status === 'published'
                          ? 'status-published'
                          : video.status === 'draft'
                          ? 'status-draft'
                          : 'status-inactive'
                      }>
                        {video.status}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Tags */}
            {video.tags && video.tags.length > 0 && (
              <div className="card">
                <div className="card-body">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {video.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
