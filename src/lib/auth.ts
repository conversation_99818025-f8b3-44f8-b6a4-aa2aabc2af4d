import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { MongoDBAdapter } from '@next-auth/mongodb-adapter';
import bcrypt from 'bcryptjs';
import { ObjectId } from 'mongodb';
import { NextRequest, NextResponse } from 'next/server';
import clientPromise, { getCollection } from './mongodb';
import { User, ExtendedUser } from '@/types';

// Extend NextAuth types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: 'admin' | 'editor' | 'viewer';
      avatar?: string;
      image?: string;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: 'admin' | 'editor' | 'viewer';
    avatar?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: 'admin' | 'editor' | 'viewer';
    avatar?: string;
  }
}

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const users = await getCollection('users');
          const user = await users.findOne({ email: credentials.email }) as User;

          if (!user || !user.isActive) {
            return null;
          }

          // Check if user has a password field (for credential-based auth)
          const userWithPassword = user as User & { password?: string };
          if (!userWithPassword.password) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            userWithPassword.password
          );

          if (!isPasswordValid) {
            return null;
          }

          // Update last login
          await users.updateOne(
            { _id: new ObjectId(user._id) },
            { $set: { lastLogin: new Date() } }
          );

          return {
            id: user._id?.toString() || '',
            email: user.email,
            name: user.name,
            role: user.role,
            avatar: user.avatar,
          } as ExtendedUser;
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        const extendedUser = user as ExtendedUser;
        token.role = extendedUser.role;
        token.avatar = extendedUser.avatar;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub || '';
        session.user.role = token.role as 'admin' | 'editor' | 'viewer';
        session.user.avatar = token.avatar;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Helper function to hash passwords
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// Helper function to create admin user
export async function createAdminUser(email: string, password: string, name: string) {
  try {
    const users = await getCollection('users');
    
    // Check if user already exists
    const existingUser = await users.findOne({ email });
    if (existingUser) {
      throw new Error('User already exists');
    }

    const hashedPassword = await hashPassword(password);
    
    const newUser = {
      email,
      name,
      password: hashedPassword,
      role: 'admin' as const,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      permissions: [
        'videos:read',
        'videos:write',
        'videos:delete',
        'flows:read',
        'flows:write',
        'flows:delete',
        'analytics:read',
        'users:read',
        'users:write',
        'users:delete',
        'settings:read',
        'settings:write'
      ]
    };

    const result = await users.insertOne(newUser);
    return result.insertedId;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}

// Helper function to check permissions
export function hasPermission(userRole: string, permission: string): boolean {
  const rolePermissions: Record<string, string[]> = {
    admin: [
      'videos:read', 'videos:write', 'videos:delete',
      'flows:read', 'flows:write', 'flows:delete',
      'analytics:read',
      'users:read', 'users:write', 'users:delete',
      'settings:read', 'settings:write'
    ],
    editor: [
      'videos:read', 'videos:write',
      'flows:read', 'flows:write',
      'analytics:read'
    ],
    viewer: [
      'videos:read',
      'flows:read',
      'analytics:read'
    ]
  };

  return rolePermissions[userRole]?.includes(permission) || false;
}

// Middleware helper for API routes
export function requireAuth(requiredPermission?: string) {
  return async (req: NextRequest & { session?: unknown; user?: ExtendedUser }, res: NextResponse, next: () => void) => {
    try {
      // This would be implemented with getServerSession in API routes
      // For now, this is a placeholder structure
      const session = req.session as { user?: ExtendedUser } | undefined;

      if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      if (requiredPermission && !hasPermission(session.user.role, requiredPermission)) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      req.user = session.user;
      next();
    } catch {
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
  };
}

// Helper function to get user role from session
export function getUserRole(session: { user?: ExtendedUser } | null): 'admin' | 'editor' | 'viewer' | null {
  return session?.user?.role || null;
}

// Helper function to check if user has required role
export function hasRequiredRole(userRole: string, requiredRoles: string[]): boolean {
  return requiredRoles.includes(userRole);
}