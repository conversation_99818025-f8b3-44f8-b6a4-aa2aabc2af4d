import { MongoClient, Db, Document, Filter, UpdateFilter, OptionalUnlessRequiredId } from 'mongodb';

if (!process.env.MONGODB_URI) {
  throw new Error('Invalid/Missing environment variable: "MONGODB_URI"');
}

const uri = process.env.MONGODB_URI;
const options = {};

let client: MongoClient;
let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  const globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options);
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

// Export a module-scoped MongoClient promise. By doing this in a
// separate module, the client can be shared across functions.
export default clientPromise;

// Helper function to get database
export async function getDatabase(): Promise<Db> {
  const client = await clientPromise;
  return client.db(process.env.MONGODB_DB || 'quest_vr_training');
}

// Helper function to get collection
export async function getCollection(collectionName: string) {
  const db = await getDatabase();
  return db.collection(collectionName);
}

// MongoDB Atlas Data API helper
export class MongoDataAPI {
  private baseUrl: string;
  private apiKey: string;
  private dataSource: string;
  private database: string;

  constructor() {
    this.baseUrl = process.env.MONGODB_DATA_API_URL || '';
    this.apiKey = process.env.MONGODB_API_KEY || '';
    this.dataSource = process.env.MONGODB_DATA_SOURCE || '';
    this.database = process.env.MONGODB_DB || 'quest_vr_training';
  }

  private async makeRequest(action: string, payload: Record<string, unknown>) {
    const response = await fetch(`${this.baseUrl}/${action}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': this.apiKey,
      },
      body: JSON.stringify({
        dataSource: this.dataSource,
        database: this.database,
        ...payload,
      }),
    });

    if (!response.ok) {
      throw new Error(`MongoDB Data API error: ${response.statusText}`);
    }

    return response.json();
  }

  async find(collection: string, filter: Filter<Document> = {}, options: Record<string, unknown> = {}) {
    return this.makeRequest('find', {
      collection,
      filter,
      ...options,
    });
  }

  async findOne(collection: string, filter: Filter<Document> = {}) {
    return this.makeRequest('findOne', {
      collection,
      filter,
    });
  }

  async insertOne(collection: string, document: OptionalUnlessRequiredId<Document>) {
    return this.makeRequest('insertOne', {
      collection,
      document,
    });
  }

  async insertMany(collection: string, documents: OptionalUnlessRequiredId<Document>[]) {
    return this.makeRequest('insertMany', {
      collection,
      documents,
    });
  }

  async updateOne(collection: string, filter: Filter<Document>, update: UpdateFilter<Document>) {
    return this.makeRequest('updateOne', {
      collection,
      filter,
      update,
    });
  }

  async updateMany(collection: string, filter: Filter<Document>, update: UpdateFilter<Document>) {
    return this.makeRequest('updateMany', {
      collection,
      filter,
      update,
    });
  }

  async deleteOne(collection: string, filter: Filter<Document>) {
    return this.makeRequest('deleteOne', {
      collection,
      filter,
    });
  }

  async deleteMany(collection: string, filter: Filter<Document>) {
    return this.makeRequest('deleteMany', {
      collection,
      filter,
    });
  }

  async aggregate(collection: string, pipeline: Document[]) {
    return this.makeRequest('aggregate', {
      collection,
      pipeline,
    });
  }
}

export const mongoDataAPI = new MongoDataAPI();