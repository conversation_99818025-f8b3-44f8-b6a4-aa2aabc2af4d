{"name": "admin-panel", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@next-auth/mongodb-adapter": "^1.1.3", "@types/bcryptjs": "^2.4.6", "@types/mongodb": "^4.0.6", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.539.0", "mongodb": "^6.18.0", "next": "15.4.6", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "zod": "^4.0.16"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "typescript": "^5"}}